# 📊 Medium Analytics Bot

Sistema completo de análise de métricas do Medium com automação de divulgação no LinkedIn, utilizando AWS Free Tier.

## 🎯 Funcionalidades

- **📈 Analytics Avançado**: Coleta e análise detalhada de métricas do Medium
- **🤖 Automação LinkedIn**: Publicação automática quando novos artigos são detectados
- **📊 Dashboard Inteligente**: Visualizações em tempo real com Streamlit
- **☁️ AWS Free Tier**: Infraestrutura gratuita por 12 meses
- **📧 Relatórios Automáticos**: Insights semanais por email

## 🏗️ Arquitetura

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Medium RSS    │───▶│  Lambda Checker │───▶│   PostgreSQL    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                │                       │
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│ LinkedIn API    │◀───│  FastAPI Backend│◀───│  Redis Cache    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                │
                       ┌─────────────────┐
                       │Streamlit Dashboard│
                       └─────────────────┘
```

## 🚀 Quick Start

### 1. Desenvolvimento Local

```bash
# Clone o repositório
git clone <seu-repositorio>
cd analytics-bot

# Copie as configurações
cp .env.example .env
# Edite o .env com suas configurações

# Inicie os serviços
docker-compose up -d

# Acesse o dashboard
open http://localhost:8501
```

### 2. Deploy na AWS

```bash
# Configure AWS CLI
aws configure

# Deploy da infraestrutura
cd terraform
terraform init
terraform apply

# Configure a aplicação
./scripts/deploy.sh
```

## 📁 Estrutura do Projeto

```
analytics-bot/
├── backend/                 # API FastAPI
│   ├── app/
│   │   ├── api/            # Endpoints REST
│   │   ├── core/           # Configurações
│   │   ├── models/         # Modelos SQLAlchemy
│   │   ├── services/       # Lógica de negócio
│   │   ├── tasks/          # Tarefas Celery
│   │   └── utils/          # Utilitários
│   ├── lambda/             # Funções AWS Lambda
│   └── scripts/            # Scripts de deploy
├── dashboard/              # Interface Streamlit
│   ├── pages/             # Páginas do dashboard
│   ├── components/        # Componentes reutilizáveis
│   └── utils/             # Utilitários do dashboard
├── terraform/             # Infraestrutura como código
└── docs/                  # Documentação
```

## 🔧 Configuração

### APIs Necessárias

1. **Medium RSS**: `https://medium.com/feed/@seu_usuario`
2. **LinkedIn API**: Criar app em [LinkedIn Developers](https://www.linkedin.com/developers/)
3. **AWS Account**: Para infraestrutura (Free Tier)

### Variáveis de Ambiente

Copie `.env.example` para `.env` e configure:

```env
# Medium
MEDIUM_RSS_URL=https://medium.com/feed/@seu_usuario

# LinkedIn
LINKEDIN_CLIENT_ID=seu_client_id
LINKEDIN_CLIENT_SECRET=seu_client_secret

# AWS (para produção)
AWS_REGION=us-east-1
AWS_ACCESS_KEY_ID=sua_access_key
AWS_SECRET_ACCESS_KEY=sua_secret_key
```

## 📊 Dashboard

O dashboard Streamlit oferece:

- **📈 Overview**: Métricas gerais e tendências
- **📝 Artigos**: Lista detalhada com performance
- **👥 Audiência**: Análise de engajamento
- **🔗 LinkedIn**: Performance dos posts automáticos
- **⚙️ AWS Status**: Monitoramento da infraestrutura

## 🤖 Automação

### Fluxo Automático

1. **Lambda verifica RSS** a cada hora
2. **Detecta novos artigos** automaticamente
3. **Agenda post LinkedIn** 2h após publicação
4. **Coleta métricas** periodicamente
5. **Envia relatório semanal** por email

### Tarefas Celery

- `check_medium_rss`: Verificação do RSS
- `publish_to_linkedin`: Publicação automática
- `collect_analytics`: Coleta de métricas
- `send_weekly_report`: Relatório semanal

## 💰 Custos AWS

### Free Tier (12 meses)

| Serviço | Limite | Uso Estimado | Status |
|---------|--------|--------------|--------|
| EC2 t2.micro | 750h/mês | 720h | ✅ |
| RDS db.t3.micro | 750h/mês | 720h | ✅ |
| ElastiCache | 750h/mês | 720h | ✅ |
| Lambda | 1M requests | ~10k | ✅ |
| S3 | 5GB | <1GB | ✅ |
| SES | 62k emails | ~200 | ✅ |

**Total: $0/mês** durante 12 meses! 🎉

## 🔍 Monitoramento

### Métricas Coletadas

- **Views**: Visualizações dos artigos
- **Reads**: Leituras completas
- **Claps**: Aplausos recebidos
- **Read Ratio**: Taxa de leitura
- **LinkedIn Performance**: Impressões, cliques, engajamento

### Alertas

- Novos artigos detectados
- Performance excepcional
- Erros do sistema
- Limites AWS próximos

## 🛠️ Desenvolvimento

### Comandos Úteis

```bash
# Desenvolvimento local
docker-compose up -d
docker-compose logs -f backend

# Testes
cd backend
pytest

# Migrações
alembic revision --autogenerate -m "descrição"
alembic upgrade head

# Celery (desenvolvimento)
celery -A app.tasks.celery worker --loglevel=info
celery -A app.tasks.celery beat --loglevel=info
```

### Estrutura de Branches

- `main`: Produção
- `develop`: Desenvolvimento
- `feature/*`: Novas funcionalidades
- `hotfix/*`: Correções urgentes

## 📚 Documentação

- [Setup AWS](docs/aws-setup.md)
- [Deploy Guide](docs/deployment.md)
- [API Documentation](http://localhost:8000/docs)
- [Troubleshooting](docs/troubleshooting.md)

## 🤝 Contribuição

1. Fork o projeto
2. Crie uma branch (`git checkout -b feature/nova-funcionalidade`)
3. Commit suas mudanças (`git commit -am 'Adiciona nova funcionalidade'`)
4. Push para a branch (`git push origin feature/nova-funcionalidade`)
5. Abra um Pull Request

## 📄 Licença

Este projeto está sob a licença MIT. Veja o arquivo [LICENSE](LICENSE) para detalhes.

## 🆘 Suporte

- 📧 Email: <EMAIL>
- 💬 Issues: [GitHub Issues](https://github.com/seu-usuario/analytics-bot/issues)
- 📖 Wiki: [GitHub Wiki](https://github.com/seu-usuario/analytics-bot/wiki)

---

**Feito com ❤️ para criadores de conteúdo**
