<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <style>
        body {
            width: 350px;
            min-height: 400px;
            margin: 0;
            padding: 20px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .header {
            text-align: center;
            margin-bottom: 20px;
        }
        
        .header h2 {
            margin: 0;
            font-size: 18px;
            font-weight: 600;
        }
        
        .header p {
            margin: 5px 0 0 0;
            font-size: 12px;
            opacity: 0.8;
        }
        
        .status-card {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            padding: 15px;
            margin-bottom: 15px;
            backdrop-filter: blur(10px);
        }
        
        .status-indicator {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
        }
        
        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-dot.connected { background: #4ade80; }
        .status-dot.disconnected { background: #f87171; }
        .status-dot.working { background: #fbbf24; animation: pulse 2s infinite; }
        
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
        
        .btn {
            width: 100%;
            padding: 12px;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-bottom: 10px;
        }
        
        .btn-primary {
            background: #10b981;
            color: white;
        }
        
        .btn-primary:hover {
            background: #059669;
            transform: translateY(-1px);
        }
        
        .btn-secondary {
            background: rgba(255, 255, 255, 0.2);
            color: white;
        }
        
        .btn-secondary:hover {
            background: rgba(255, 255, 255, 0.3);
        }
        
        .btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        
        .progress-container {
            margin: 15px 0;
        }
        
        .progress-bar {
            width: 100%;
            height: 6px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 3px;
            overflow: hidden;
        }
        
        .progress-fill {
            height: 100%;
            background: #10b981;
            width: 0%;
            transition: width 0.3s ease;
        }
        
        .progress-text {
            font-size: 12px;
            text-align: center;
            margin-top: 8px;
            opacity: 0.9;
        }
        
        .results {
            background: rgba(16, 185, 129, 0.2);
            border-radius: 8px;
            padding: 12px;
            margin-top: 15px;
        }
        
        .results h4 {
            margin: 0 0 8px 0;
            font-size: 14px;
        }
        
        .results p {
            margin: 4px 0;
            font-size: 12px;
        }
        
        .error {
            background: rgba(248, 113, 113, 0.2);
            border-radius: 8px;
            padding: 12px;
            margin-top: 15px;
        }
        
        .settings {
            margin-top: 15px;
            padding-top: 15px;
            border-top: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .settings label {
            display: block;
            font-size: 12px;
            margin-bottom: 5px;
            opacity: 0.9;
        }
        
        .settings input {
            width: 100%;
            padding: 8px;
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 4px;
            background: rgba(255, 255, 255, 0.1);
            color: white;
            font-size: 12px;
        }
        
        .settings input::placeholder {
            color: rgba(255, 255, 255, 0.6);
        }
        
        .hidden {
            display: none;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
            margin-top: 10px;
        }
        
        .stat-item {
            text-align: center;
            background: rgba(255, 255, 255, 0.1);
            padding: 8px;
            border-radius: 6px;
        }
        
        .stat-number {
            font-size: 16px;
            font-weight: 600;
            display: block;
        }
        
        .stat-label {
            font-size: 10px;
            opacity: 0.8;
        }
    </style>
</head>
<body>
    <div class="header">
        <h2>📊 Medium Analytics</h2>
        <p>Extrator de Métricas</p>
    </div>
    
    <div class="status-card">
        <div class="status-indicator">
            <div class="status-dot" id="status-dot"></div>
            <span id="status-text">Verificando conexão...</span>
        </div>
        <div id="page-info"></div>
    </div>
    
    <!-- Botões principais -->
    <div id="main-actions">
        <button class="btn btn-primary" id="extract-btn">
            🚀 Extrair Todas as Métricas
        </button>
        <button class="btn btn-secondary" id="test-btn">
            🧪 Testar Página Atual
        </button>
    </div>
    
    <!-- Progresso -->
    <div class="progress-container hidden" id="progress-container">
        <div class="progress-bar">
            <div class="progress-fill" id="progress-fill"></div>
        </div>
        <div class="progress-text" id="progress-text">Iniciando...</div>
    </div>
    
    <!-- Resultados -->
    <div class="results hidden" id="results">
        <h4>✅ Extração Concluída!</h4>
        <div class="stats-grid">
            <div class="stat-item">
                <span class="stat-number" id="articles-found">0</span>
                <span class="stat-label">Artigos</span>
            </div>
            <div class="stat-item">
                <span class="stat-number" id="articles-sent">0</span>
                <span class="stat-label">Enviados</span>
            </div>
        </div>
        <p>Tempo: <span id="extraction-time">0s</span></p>
        <button class="btn btn-secondary" id="view-dashboard">
            📊 Ver Dashboard
        </button>
    </div>
    
    <!-- Erro -->
    <div class="error hidden" id="error">
        <h4>❌ Erro na Extração</h4>
        <p id="error-message"></p>
        <button class="btn btn-secondary" id="retry-btn">
            🔄 Tentar Novamente
        </button>
    </div>
    
    <!-- Configurações -->
    <div class="settings">
        <label for="api-url">🔧 URL da API:</label>
        <input type="text" id="api-url" placeholder="http://localhost:8000" value="http://localhost:8000">
    </div>
    
    <script src="popup.js"></script>
</body>
</html>
