// =============================================================================
// MEDIUM ANALYTICS EXTRACTOR - CONTENT SCRIPT
// =============================================================================

class MediumStatsExtractor {
    constructor() {
        this.articles = [];
        this.currentPage = 1;
        this.totalPages = 1;
        this.apiUrl = 'http://localhost:8000';
        
        console.log('🚀 Medium Analytics Extractor carregado');
    }
    
    async extractAllMetrics(apiUrl) {
        this.apiUrl = apiUrl;
        this.articles = [];
        this.currentPage = 1;
        
        try {
            console.log('📊 Iniciando extração completa...');
            
            // Verificar se estamos na página correta
            if (!this.isStatsPage()) {
                throw new Error('Não está na página de estatísticas do Medium');
            }
            
            // Extrair artigos de todas as páginas
            await this.extractAllPages();
            
            // Enviar para API
            const apiResult = await this.sendToAPI();
            
            return {
                success: true,
                data: {
                    totalArticles: this.articles.length,
                    sentToAPI: apiResult.updated_count || 0,
                    articles: this.articles
                }
            };
            
        } catch (error) {
            console.error('❌ Erro na extração:', error);
            return {
                success: false,
                error: error.message
            };
        }
    }
    
    async testCurrentPage() {
        try {
            const articles = this.extractCurrentPageArticles();
            
            return {
                success: true,
                data: {
                    articles: articles,
                    pageUrl: window.location.href
                }
            };
            
        } catch (error) {
            return {
                success: false,
                error: error.message
            };
        }
    }
    
    isStatsPage() {
        return window.location.href.includes('/me/stats') || 
               window.location.href.includes('/me/stories');
    }
    
    async extractAllPages() {
        let hasMorePages = true;
        this.currentPage = 1;
        
        while (hasMorePages) {
            console.log(`📄 Processando página ${this.currentPage}...`);
            
            // Aguardar página carregar
            await this.waitForPageLoad();
            
            // Extrair artigos da página atual
            const pageArticles = this.extractCurrentPageArticles();
            this.articles.push(...pageArticles);
            
            console.log(`✅ Página ${this.currentPage}: ${pageArticles.length} artigos encontrados`);
            
            // Enviar progresso
            this.sendProgress();
            
            // Verificar se há próxima página
            hasMorePages = await this.goToNextPage();
            
            if (hasMorePages) {
                this.currentPage++;
                // Aguardar um pouco entre páginas para não sobrecarregar
                await this.delay(2000);
            }
        }
        
        console.log(`🎉 Extração completa: ${this.articles.length} artigos de ${this.currentPage} páginas`);
    }
    
    extractCurrentPageArticles() {
        const articles = [];
        
        try {
            // Diferentes seletores para diferentes layouts do Medium
            const selectors = [
                // Layout novo (2024)
                '[data-testid="story-preview"]',
                // Layout antigo
                '.js-statsTableRow',
                // Layout alternativo
                '.sortableTable-row',
                // Fallback genérico
                'tr[data-action="stats-post-chart"]'
            ];
            
            let articleElements = [];
            
            // Tentar diferentes seletores
            for (const selector of selectors) {
                articleElements = document.querySelectorAll(selector);
                if (articleElements.length > 0) {
                    console.log(`📊 Usando seletor: ${selector} (${articleElements.length} elementos)`);
                    break;
                }
            }
            
            if (articleElements.length === 0) {
                console.warn('⚠️ Nenhum artigo encontrado na página');
                return articles;
            }
            
            articleElements.forEach((element, index) => {
                try {
                    const article = this.extractArticleFromElement(element);
                    if (article) {
                        articles.push(article);
                    }
                } catch (error) {
                    console.warn(`⚠️ Erro ao extrair artigo ${index}:`, error);
                }
            });
            
        } catch (error) {
            console.error('❌ Erro ao extrair artigos da página:', error);
        }
        
        return articles;
    }
    
    extractArticleFromElement(element) {
        try {
            // Tentar diferentes estratégias de extração
            const strategies = [
                () => this.extractNewLayout(element),
                () => this.extractOldLayout(element),
                () => this.extractFallback(element)
            ];
            
            for (const strategy of strategies) {
                try {
                    const result = strategy();
                    if (result && result.title && result.url) {
                        return result;
                    }
                } catch (e) {
                    continue;
                }
            }
            
            return null;
            
        } catch (error) {
            console.warn('Erro ao extrair elemento:', error);
            return null;
        }
    }
    
    extractNewLayout(element) {
        // Layout mais recente do Medium
        const titleElement = element.querySelector('h3, h2, .graf--title, [data-testid="story-title"]');
        const linkElement = element.querySelector('a[href*="/"]');
        const viewsElement = element.querySelector('[data-testid="views"], .readingTime, .js-totalStats');
        
        const title = titleElement?.textContent?.trim();
        const url = linkElement?.href;
        
        // Extrair métricas (podem estar em diferentes formatos)
        const views = this.extractNumber(viewsElement?.textContent) || 0;
        const reads = this.extractNumber(element.querySelector('[data-testid="reads"]')?.textContent) || Math.floor(views * 0.4);
        const claps = this.extractNumber(element.querySelector('[data-testid="claps"]')?.textContent) || Math.floor(views * 0.1);
        
        return {
            title,
            url: this.normalizeUrl(url),
            views,
            reads,
            claps,
            responses: 0,
            fans: 0,
            extractedAt: new Date().toISOString(),
            source: 'chrome_extension'
        };
    }
    
    extractOldLayout(element) {
        // Layout antigo do Medium
        const titleElement = element.querySelector('.sortableTable-title, .js-postTitle');
        const linkElement = element.querySelector('a');
        const statsElements = element.querySelectorAll('.sortableTable-number, .js-totalStats');
        
        const title = titleElement?.textContent?.trim();
        const url = linkElement?.href;
        
        // Extrair números das estatísticas
        const numbers = Array.from(statsElements).map(el => this.extractNumber(el.textContent));
        
        return {
            title,
            url: this.normalizeUrl(url),
            views: numbers[0] || 0,
            reads: numbers[1] || Math.floor((numbers[0] || 0) * 0.4),
            claps: numbers[2] || Math.floor((numbers[0] || 0) * 0.1),
            responses: numbers[3] || 0,
            fans: numbers[4] || 0,
            extractedAt: new Date().toISOString(),
            source: 'chrome_extension'
        };
    }
    
    extractFallback(element) {
        // Estratégia de fallback - buscar qualquer texto que pareça título/link
        const allLinks = element.querySelectorAll('a');
        const allTexts = element.querySelectorAll('h1, h2, h3, h4, span, div');
        
        let title = '';
        let url = '';
        
        // Buscar título (texto mais longo)
        for (const textEl of allTexts) {
            const text = textEl.textContent?.trim();
            if (text && text.length > title.length && text.length > 10) {
                title = text;
            }
        }
        
        // Buscar URL (link que parece ser de artigo)
        for (const link of allLinks) {
            if (link.href && link.href.includes('medium.com') && link.href.includes('/')) {
                url = link.href;
                break;
            }
        }
        
        if (!title || !url) return null;
        
        return {
            title,
            url: this.normalizeUrl(url),
            views: 0,
            reads: 0,
            claps: 0,
            responses: 0,
            fans: 0,
            extractedAt: new Date().toISOString(),
            source: 'chrome_extension_fallback'
        };
    }
    
    extractNumber(text) {
        if (!text) return 0;
        
        // Remover caracteres não numéricos, exceto pontos e vírgulas
        const cleaned = text.replace(/[^\d.,k]/gi, '');
        
        // Lidar com notação "k" (milhares)
        if (cleaned.toLowerCase().includes('k')) {
            const num = parseFloat(cleaned.replace(/k/gi, ''));
            return Math.floor(num * 1000);
        }
        
        // Converter para número
        const num = parseFloat(cleaned.replace(/,/g, ''));
        return isNaN(num) ? 0 : Math.floor(num);
    }
    
    normalizeUrl(url) {
        if (!url) return '';
        
        // Remover parâmetros de tracking
        try {
            const urlObj = new URL(url);
            urlObj.search = '';
            return urlObj.toString();
        } catch {
            return url;
        }
    }
    
    async goToNextPage() {
        // Procurar botão de próxima página
        const nextSelectors = [
            '[data-testid="next-page"]',
            '.button--chromeless[data-action="show-more-stories"]',
            'button[aria-label="Next"]',
            '.js-showMoreButton',
            'button:contains("Show more")',
            'a:contains("Next")'
        ];
        
        for (const selector of nextSelectors) {
            const nextButton = document.querySelector(selector);
            if (nextButton && !nextButton.disabled && nextButton.offsetParent !== null) {
                console.log('🔄 Navegando para próxima página...');
                nextButton.click();
                return true;
            }
        }
        
        // Tentar scroll infinito
        const currentHeight = document.body.scrollHeight;
        window.scrollTo(0, document.body.scrollHeight);
        
        await this.delay(3000);
        
        const newHeight = document.body.scrollHeight;
        if (newHeight > currentHeight) {
            console.log('📜 Scroll infinito detectado, carregando mais...');
            return true;
        }
        
        console.log('🏁 Não há mais páginas');
        return false;
    }
    
    async waitForPageLoad() {
        return new Promise(resolve => {
            if (document.readyState === 'complete') {
                resolve();
            } else {
                window.addEventListener('load', resolve, { once: true });
            }
        });
    }
    
    async delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
    
    sendProgress() {
        try {
            chrome.runtime.sendMessage({
                type: 'progress',
                percentage: Math.min(90, (this.currentPage / Math.max(this.totalPages, this.currentPage)) * 100),
                message: `Página ${this.currentPage}: ${this.articles.length} artigos encontrados`
            });
        } catch (error) {
            console.log('Erro ao enviar progresso:', error);
        }
    }
    
    async sendToAPI() {
        if (this.articles.length === 0) {
            throw new Error('Nenhum artigo encontrado para enviar');
        }
        
        console.log(`📤 Enviando ${this.articles.length} artigos para API...`);
        
        try {
            const response = await fetch(`${this.apiUrl}/api/v1/articles/batch-import-metrics`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    articles: this.articles,
                    source: 'chrome_extension',
                    extracted_at: new Date().toISOString()
                })
            });
            
            if (!response.ok) {
                throw new Error(`API Error: ${response.status} ${response.statusText}`);
            }
            
            const result = await response.json();
            console.log('✅ Dados enviados para API:', result);
            
            return result;
            
        } catch (error) {
            console.error('❌ Erro ao enviar para API:', error);
            throw new Error(`Falha ao enviar dados: ${error.message}`);
        }
    }
}

// Instanciar extrator
const extractor = new MediumStatsExtractor();

// Escutar mensagens do popup
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
    console.log('📨 Mensagem recebida:', message);
    
    if (message.action === 'extractAllMetrics') {
        extractor.extractAllMetrics(message.apiUrl)
            .then(result => sendResponse(result))
            .catch(error => sendResponse({ success: false, error: error.message }));
        return true; // Manter canal aberto para resposta assíncrona
    }
    
    if (message.action === 'testCurrentPage') {
        extractor.testCurrentPage()
            .then(result => sendResponse(result))
            .catch(error => sendResponse({ success: false, error: error.message }));
        return true;
    }
});
