// =============================================================================
// MEDIUM ANALYTICS EXTRACTOR - BACKGROUND SCRIPT
// =============================================================================

class ExtensionBackground {
    constructor() {
        this.setupEventListeners();
        console.log('🔧 Medium Analytics Extension - Background script carregado');
    }
    
    setupEventListeners() {
        // Quando extensão é instalada
        chrome.runtime.onInstalled.addListener((details) => {
            console.log('📦 Extensão instalada:', details.reason);
            
            if (details.reason === 'install') {
                this.onFirstInstall();
            } else if (details.reason === 'update') {
                this.onUpdate(details.previousVersion);
            }
        });
        
        // Quando aba é atualizada
        chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
            if (changeInfo.status === 'complete' && tab.url) {
                this.onTabUpdated(tab);
            }
        });
        
        // Mensagens entre scripts
        chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
            this.handleMessage(message, sender, sendResponse);
            return true; // Manter canal aberto
        });
    }
    
    onFirstInstall() {
        console.log('🎉 Primeira instalação da extensão');
        
        // Configurações padrão
        chrome.storage.sync.set({
            apiUrl: 'http://localhost:8000',
            autoExtract: false,
            extractionInterval: 24, // horas
            lastExtraction: null
        });
        
        // Abrir página de boas-vindas
        chrome.tabs.create({
            url: chrome.runtime.getURL('welcome.html')
        });
    }
    
    onUpdate(previousVersion) {
        console.log(`🔄 Extensão atualizada de ${previousVersion} para ${chrome.runtime.getManifest().version}`);
        
        // Migrar configurações se necessário
        this.migrateSettings(previousVersion);
    }
    
    onTabUpdated(tab) {
        // Verificar se é página do Medium
        if (tab.url.includes('medium.com')) {
            // Atualizar ícone da extensão
            if (tab.url.includes('/me/stats')) {
                this.setIcon('active', tab.id);
            } else {
                this.setIcon('inactive', tab.id);
            }
        }
    }
    
    setIcon(state, tabId) {
        const iconPaths = {
            active: {
                16: 'icons/icon16-active.png',
                32: 'icons/icon32-active.png',
                48: 'icons/icon48-active.png',
                128: 'icons/icon128-active.png'
            },
            inactive: {
                16: 'icons/icon16.png',
                32: 'icons/icon32.png',
                48: 'icons/icon48.png',
                128: 'icons/icon128.png'
            }
        };
        
        chrome.action.setIcon({
            path: iconPaths[state],
            tabId: tabId
        });
    }
    
    handleMessage(message, sender, sendResponse) {
        switch (message.type) {
            case 'getSettings':
                this.getSettings().then(sendResponse);
                break;
                
            case 'saveSettings':
                this.saveSettings(message.settings).then(sendResponse);
                break;
                
            case 'logExtraction':
                this.logExtraction(message.data);
                sendResponse({ success: true });
                break;
                
            case 'getExtractionHistory':
                this.getExtractionHistory().then(sendResponse);
                break;
                
            default:
                console.log('Mensagem não reconhecida:', message);
        }
    }
    
    async getSettings() {
        try {
            const settings = await chrome.storage.sync.get([
                'apiUrl',
                'autoExtract',
                'extractionInterval',
                'lastExtraction'
            ]);
            
            return {
                success: true,
                settings: {
                    apiUrl: settings.apiUrl || 'http://localhost:8000',
                    autoExtract: settings.autoExtract || false,
                    extractionInterval: settings.extractionInterval || 24,
                    lastExtraction: settings.lastExtraction || null
                }
            };
        } catch (error) {
            return { success: false, error: error.message };
        }
    }
    
    async saveSettings(newSettings) {
        try {
            await chrome.storage.sync.set(newSettings);
            return { success: true };
        } catch (error) {
            return { success: false, error: error.message };
        }
    }
    
    async logExtraction(data) {
        try {
            // Salvar histórico de extrações
            const history = await this.getExtractionHistory();
            
            const extraction = {
                timestamp: new Date().toISOString(),
                articlesFound: data.articlesFound || 0,
                articlesSent: data.articlesSent || 0,
                duration: data.duration || 0,
                success: data.success || false,
                error: data.error || null
            };
            
            history.extractions.unshift(extraction);
            
            // Manter apenas últimas 50 extrações
            if (history.extractions.length > 50) {
                history.extractions = history.extractions.slice(0, 50);
            }
            
            await chrome.storage.local.set({ extractionHistory: history });
            
            // Atualizar última extração
            await chrome.storage.sync.set({ lastExtraction: extraction.timestamp });
            
        } catch (error) {
            console.error('Erro ao salvar histórico:', error);
        }
    }
    
    async getExtractionHistory() {
        try {
            const result = await chrome.storage.local.get(['extractionHistory']);
            
            return result.extractionHistory || {
                extractions: [],
                totalArticles: 0,
                totalExtractions: 0
            };
        } catch (error) {
            console.error('Erro ao obter histórico:', error);
            return { extractions: [], totalArticles: 0, totalExtractions: 0 };
        }
    }
    
    async migrateSettings(previousVersion) {
        // Implementar migrações de configurações se necessário
        console.log(`Migrando configurações da versão ${previousVersion}`);
        
        // Exemplo de migração
        if (previousVersion < '1.0.0') {
            // Migrar configurações antigas
        }
    }
    
    // Função para extrações automáticas (futuro)
    async scheduleAutoExtraction() {
        const settings = await this.getSettings();
        
        if (settings.settings.autoExtract) {
            // Implementar agendamento automático
            console.log('Agendamento automático habilitado');
        }
    }
}

// Inicializar background script
new ExtensionBackground();
