#!/usr/bin/env python3
"""
Script para criar ícones da extensão Chrome
"""

import os
from PIL import Image, ImageDraw, ImageFont

def create_icon(size, filename, active=False):
    """Criar ícone da extensão"""
    
    # Cores
    bg_color = "#667eea" if not active else "#10b981"
    text_color = "white"
    
    # Criar imagem
    img = Image.new('RGBA', (size, size), (0, 0, 0, 0))
    draw = ImageDraw.Draw(img)
    
    # Desenhar círculo de fundo
    margin = size // 8
    draw.ellipse([margin, margin, size-margin, size-margin], fill=bg_color)
    
    # Desenhar ícone de gráfico (barras)
    bar_width = size // 12
    bar_spacing = size // 16
    start_x = size // 3
    start_y = size // 3
    
    # Três barras de alturas diferentes
    heights = [size//6, size//4, size//5]
    
    for i, height in enumerate(heights):
        x = start_x + i * (bar_width + bar_spacing)
        y = start_y + (size//4 - height)
        draw.rectangle([x, y, x + bar_width, start_y + size//4], fill=text_color)
    
    # Salvar
    img.save(filename, 'PNG')
    print(f"✅ Criado: {filename}")

def main():
    """Criar todos os ícones"""
    
    # Ícones normais
    sizes = [16, 32, 48, 128]
    
    for size in sizes:
        create_icon(size, f"icon{size}.png", active=False)
        create_icon(size, f"icon{size}-active.png", active=True)
    
    print("🎉 Todos os ícones criados!")

if __name__ == "__main__":
    main()
