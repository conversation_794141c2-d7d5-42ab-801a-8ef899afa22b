// =============================================================================
// MEDIUM ANALYTICS EXTRACTOR - POPUP CONTROLLER
// =============================================================================

class ExtensionPopup {
    constructor() {
        this.apiUrl = 'http://localhost:8000';
        this.isExtracting = false;
        this.startTime = null;
        
        this.initializeElements();
        this.loadSettings();
        this.checkPageStatus();
        this.bindEvents();
    }
    
    initializeElements() {
        // Status elements
        this.statusDot = document.getElementById('status-dot');
        this.statusText = document.getElementById('status-text');
        this.pageInfo = document.getElementById('page-info');
        
        // Action buttons
        this.extractBtn = document.getElementById('extract-btn');
        this.testBtn = document.getElementById('test-btn');
        this.retryBtn = document.getElementById('retry-btn');
        this.viewDashboardBtn = document.getElementById('view-dashboard');
        
        // Progress elements
        this.progressContainer = document.getElementById('progress-container');
        this.progressFill = document.getElementById('progress-fill');
        this.progressText = document.getElementById('progress-text');
        
        // Result elements
        this.resultsContainer = document.getElementById('results');
        this.articlesFound = document.getElementById('articles-found');
        this.articlesSent = document.getElementById('articles-sent');
        this.extractionTime = document.getElementById('extraction-time');
        
        // Error elements
        this.errorContainer = document.getElementById('error');
        this.errorMessage = document.getElementById('error-message');
        
        // Settings
        this.apiUrlInput = document.getElementById('api-url');
    }
    
    bindEvents() {
        this.extractBtn.addEventListener('click', () => this.startExtraction());
        this.testBtn.addEventListener('click', () => this.testCurrentPage());
        this.retryBtn.addEventListener('click', () => this.hideError());
        this.viewDashboardBtn.addEventListener('click', () => this.openDashboard());
        this.apiUrlInput.addEventListener('change', () => this.saveSettings());
    }
    
    async loadSettings() {
        try {
            const result = await chrome.storage.sync.get(['apiUrl']);
            if (result.apiUrl) {
                this.apiUrl = result.apiUrl;
                this.apiUrlInput.value = result.apiUrl;
            }
        } catch (error) {
            console.log('Usando configurações padrão');
        }
    }
    
    async saveSettings() {
        this.apiUrl = this.apiUrlInput.value;
        try {
            await chrome.storage.sync.set({ apiUrl: this.apiUrl });
        } catch (error) {
            console.log('Erro ao salvar configurações:', error);
        }
    }
    
    async checkPageStatus() {
        try {
            const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
            
            if (!tab.url.includes('medium.com')) {
                this.setStatus('disconnected', 'Não está no Medium');
                this.pageInfo.innerHTML = '<small>Acesse medium.com/me/stats para usar</small>';
                this.extractBtn.disabled = true;
                this.testBtn.disabled = true;
                return;
            }
            
            if (tab.url.includes('/me/stats')) {
                this.setStatus('connected', 'Página de estatísticas detectada');
                this.pageInfo.innerHTML = '<small>✅ Pronto para extrair métricas</small>';
                this.extractBtn.disabled = false;
                this.testBtn.disabled = false;
            } else {
                this.setStatus('disconnected', 'Acesse a página de estatísticas');
                this.pageInfo.innerHTML = '<small>Vá para medium.com/me/stats</small>';
                this.extractBtn.disabled = true;
                this.testBtn.disabled = true;
            }
            
            // Testar conexão com API
            await this.testApiConnection();
            
        } catch (error) {
            this.setStatus('disconnected', 'Erro ao verificar página');
            console.error('Erro:', error);
        }
    }
    
    async testApiConnection() {
        try {
            const response = await fetch(`${this.apiUrl}/health`, { 
                method: 'GET',
                timeout: 5000 
            });
            
            if (response.ok) {
                this.pageInfo.innerHTML += '<br><small>🔗 API conectada</small>';
            } else {
                this.pageInfo.innerHTML += '<br><small>⚠️ API não responde</small>';
            }
        } catch (error) {
            this.pageInfo.innerHTML += '<br><small>❌ API offline</small>';
        }
    }
    
    setStatus(type, message) {
        this.statusDot.className = `status-dot ${type}`;
        this.statusText.textContent = message;
    }
    
    async startExtraction() {
        if (this.isExtracting) return;
        
        this.isExtracting = true;
        this.startTime = Date.now();
        
        // UI updates
        this.hideError();
        this.hideResults();
        this.showProgress();
        this.setStatus('working', 'Extraindo métricas...');
        this.extractBtn.disabled = true;
        
        try {
            // Enviar mensagem para content script
            const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
            
            const response = await chrome.tabs.sendMessage(tab.id, {
                action: 'extractAllMetrics',
                apiUrl: this.apiUrl
            });
            
            if (response.success) {
                this.showResults(response.data);
            } else {
                throw new Error(response.error || 'Erro desconhecido');
            }
            
        } catch (error) {
            this.showError(error.message);
        } finally {
            this.isExtracting = false;
            this.extractBtn.disabled = false;
            this.hideProgress();
        }
    }
    
    async testCurrentPage() {
        try {
            const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
            
            const response = await chrome.tabs.sendMessage(tab.id, {
                action: 'testCurrentPage'
            });
            
            if (response.success) {
                alert(`✅ Teste bem-sucedido!\n\nArtigos encontrados: ${response.data.articles.length}\n\nExemplo:\n${JSON.stringify(response.data.articles[0], null, 2)}`);
            } else {
                alert(`❌ Erro no teste:\n${response.error}`);
            }
            
        } catch (error) {
            alert(`❌ Erro: ${error.message}`);
        }
    }
    
    showProgress() {
        this.progressContainer.classList.remove('hidden');
        this.updateProgress(0, 'Iniciando extração...');
    }
    
    hideProgress() {
        this.progressContainer.classList.add('hidden');
    }
    
    updateProgress(percentage, message) {
        this.progressFill.style.width = `${percentage}%`;
        this.progressText.textContent = message;
    }
    
    showResults(data) {
        this.resultsContainer.classList.remove('hidden');
        this.articlesFound.textContent = data.totalArticles || 0;
        this.articlesSent.textContent = data.sentToAPI || 0;
        
        const duration = Math.round((Date.now() - this.startTime) / 1000);
        this.extractionTime.textContent = `${duration}s`;
        
        this.setStatus('connected', 'Extração concluída!');
    }
    
    hideResults() {
        this.resultsContainer.classList.add('hidden');
    }
    
    showError(message) {
        this.errorContainer.classList.remove('hidden');
        this.errorMessage.textContent = message;
        this.setStatus('disconnected', 'Erro na extração');
    }
    
    hideError() {
        this.errorContainer.classList.add('hidden');
    }
    
    openDashboard() {
        chrome.tabs.create({ url: `${this.apiUrl.replace(':8000', ':8501')}` });
    }
}

// Inicializar quando DOM estiver pronto
document.addEventListener('DOMContentLoaded', () => {
    new ExtensionPopup();
});

// Escutar mensagens do content script para atualizar progresso
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
    if (message.type === 'progress') {
        const popup = window.extensionPopup;
        if (popup) {
            popup.updateProgress(message.percentage, message.message);
        }
    }
});
