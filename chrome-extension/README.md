# 📊 Medium Analytics Extractor - Chrome Extension

Extensão Chrome para extrair métricas dos seus artigos do Medium automaticamente.

## 🚀 **Como Instalar**

### **1. <PERSON><PERSON><PERSON> a <PERSON>tensão**
```bash
# A pasta chrome-extension/ contém todos os arquivos necessários
```

### **2. Instalar no Chrome**

1. **Abra o Chrome** e vá para: `chrome://extensions/`

2. **Ative o Modo Desenvolvedor** (toggle no canto superior direito)

3. **Clique em "Carregar sem compactação"**

4. **Selecione a pasta** `chrome-extension/`

5. **✅ Extensão instalada!** Você verá o ícone na barra do Chrome

## 📋 **Como Usar**

### **Passo 1: Preparar o Sistema**
```bash
# Certifique-se de que o backend está rodando
docker-compose up -d backend

# Verificar se API está funcionando
curl http://localhost:8000/health
```

### **Passo 2: Acessar o Medium**
1. Faça login no Medium
2. Vá para: https://medium.com/me/stats
3. O ícone da extensão ficará **verde** (ativo)

### **Passo 3: Extrair Métricas**
1. **Clique no ícone** da extensão
2. **Clique em "🚀 Extrair Todas as Métricas"**
3. **Aguarde** a extração automática
4. **Veja o progresso** na interface da extensão

### **Passo 4: Ver Resultados**
1. **Clique em "📊 Ver Dashboard"** na extensão
2. Ou acesse: http://localhost:8501
3. **Explore** seus dados reais!

## 🔧 **Configurações**

### **URL da API**
- Padrão: `http://localhost:8000`
- Altere se sua API estiver em outro endereço

### **Teste de Página**
- Use "🧪 Testar Página Atual" para verificar se a extração está funcionando
- Mostra quantos artigos foram encontrados na página atual

## 📊 **O que a Extensão Extrai**

Para cada artigo:
- ✅ **Título**
- ✅ **URL**
- ✅ **Views** (visualizações)
- ✅ **Reads** (leituras)
- ✅ **Claps** (aplausos)
- ✅ **Responses** (respostas)
- ✅ **Data de extração**

## 🛡️ **Segurança e Privacidade**

### **✅ Totalmente Seguro**
- Usa **sua sessão autenticada** do Medium
- **Não armazena** senhas ou tokens
- **Não viola** termos de uso do Medium
- Dados vão **direto para sua API local**

### **✅ Código Aberto**
- Todo código está disponível para inspeção
- Sem telemetria ou tracking
- Funciona 100% offline após extração

## 🔍 **Troubleshooting**

### **Extensão não aparece ativa**
- ✅ Verifique se está em `medium.com/me/stats`
- ✅ Faça login no Medium
- ✅ Recarregue a página

### **Erro "API não responde"**
- ✅ Verifique se backend está rodando: `docker-compose ps`
- ✅ Teste a API: `curl http://localhost:8000/health`
- ✅ Verifique a URL nas configurações da extensão

### **Nenhum artigo encontrado**
- ✅ Verifique se há artigos na página de stats
- ✅ Use "🧪 Testar Página Atual" para debug
- ✅ Tente recarregar a página do Medium

### **Extração incompleta**
- ✅ Aguarde a página carregar completamente
- ✅ Desative outras extensões temporariamente
- ✅ Tente em uma aba anônima

## 📈 **Limitações**

### **Dependente da Interface**
- Se Medium mudar o layout, pode precisar de atualização
- Testado com layout atual (Dezembro 2024)

### **Rate Limiting**
- Adiciona delays entre páginas para não sobrecarregar
- Pode levar alguns minutos para muitos artigos

### **Precisão das Métricas**
- Extrai dados visíveis na página de stats
- Precisão depende do que o Medium exibe

## 🔄 **Atualizações**

### **Como Atualizar**
1. Baixe a nova versão
2. Vá para `chrome://extensions/`
3. Clique em "🔄" na extensão
4. Ou remova e reinstale

### **Changelog**
- **v1.0.0**: Versão inicial
  - Extração automática de métricas
  - Suporte a múltiplas páginas
  - Interface intuitiva
  - Integração com API

## 🆘 **Suporte**

### **Logs de Debug**
1. Abra DevTools (F12)
2. Vá para aba "Console"
3. Procure mensagens da extensão (🚀, 📊, ❌)

### **Reportar Problemas**
- Inclua logs do console
- Descreva os passos para reproduzir
- Mencione versão do Chrome e sistema operacional

## 🎯 **Próximas Funcionalidades**

- [ ] **Extração agendada** automática
- [ ] **Notificações** de novos dados
- [ ] **Export** para CSV/Excel
- [ ] **Comparação** de períodos
- [ ] **Métricas de LinkedIn** (se disponível)

---

## 🎉 **Pronto para Usar!**

A extensão está completa e pronta para extrair métricas reais dos seus artigos do Medium!

**Resultado:** De 1000 artigos em poucos minutos, automaticamente! 🚀
