-- =============================================================================
-- MEDIUM ANALYTICS BOT - INICIALIZAÇÃO DO BANCO DE DADOS
-- =============================================================================

-- Criar extensões necessárias
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";

-- Configurar timezone
SET timezone = 'UTC';

-- Criar schema para analytics (opcional, para organização)
-- CREATE SCHEMA IF NOT EXISTS analytics;

-- Função para atualizar timestamp automaticamente
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Comentário sobre o banco
COMMENT ON DATABASE medium_analytics IS 'Banco de dados para o sistema Medium Analytics Bot';

-- Configurações de performance para desenvolvimento
ALTER SYSTEM SET shared_preload_libraries = 'pg_stat_statements';
ALTER SYSTEM SET log_statement = 'all';
ALTER SYSTEM SET log_min_duration_statement = 1000;

-- Recarregar configurações
SELECT pg_reload_conf();
