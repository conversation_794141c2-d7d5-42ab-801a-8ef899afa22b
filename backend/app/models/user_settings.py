# =============================================================================
# MEDIUM ANALYTICS BOT - MODELO DE CONFIGURAÇÕES
# =============================================================================

from sqlalchemy import Column, Integer, String, DateTime, JSON
from sqlalchemy.sql import func
from typing import Any, Dict, Optional

from ..core.database import Base


class UserSettings(Base):
    """Modelo para configurações do usuário"""
    
    __tablename__ = "user_settings"
    
    # =============================================================================
    # CAMPOS PRINCIPAIS
    # =============================================================================
    
    id = Column(Integer, primary_key=True, index=True)
    key = Column(String(100), unique=True, nullable=False, index=True)
    value = Column(JSON)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    
    # =============================================================================
    # MÉTODOS
    # =============================================================================
    
    def to_dict(self) -> dict:
        """Converter para dicionário"""
        return {
            "id": self.id,
            "key": self.key,
            "value": self.value,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None
        }
    
    @classmethod
    def get_setting(cls, db, key: str, default: Any = None) -> Any:
        """
        Obter configuração por chave
        
        Args:
            db: Sessão do banco
            key: Chave da configuração
            default: Valor padrão se não encontrar
            
        Returns:
            Valor da configuração ou padrão
        """
        setting = db.query(cls).filter(cls.key == key).first()
        return setting.value if setting else default
    
    @classmethod
    def set_setting(cls, db, key: str, value: Any) -> "UserSettings":
        """
        Definir configuração
        
        Args:
            db: Sessão do banco
            key: Chave da configuração
            value: Valor a ser armazenado
            
        Returns:
            Instância da configuração
        """
        setting = db.query(cls).filter(cls.key == key).first()
        
        if setting:
            setting.value = value
        else:
            setting = cls(key=key, value=value)
            db.add(setting)
        
        db.commit()
        db.refresh(setting)
        return setting
    
    @classmethod
    def delete_setting(cls, db, key: str) -> bool:
        """
        Deletar configuração
        
        Args:
            db: Sessão do banco
            key: Chave da configuração
            
        Returns:
            True se deletou, False se não encontrou
        """
        setting = db.query(cls).filter(cls.key == key).first()
        
        if setting:
            db.delete(setting)
            db.commit()
            return True
        
        return False
    
    @classmethod
    def get_all_settings(cls, db) -> Dict[str, Any]:
        """
        Obter todas as configurações
        
        Args:
            db: Sessão do banco
            
        Returns:
            Dicionário com todas as configurações
        """
        settings = db.query(cls).all()
        return {setting.key: setting.value for setting in settings}
    
    def __repr__(self):
        return f"<UserSettings(key='{self.key}', value={self.value})>"


# =============================================================================
# CONFIGURAÇÕES PADRÃO
# =============================================================================

DEFAULT_SETTINGS = {
    # LinkedIn
    "linkedin_post_delay_hours": 2,
    "linkedin_auto_post": True,
    "linkedin_default_template": "default",
    "linkedin_include_hashtags": True,
    "linkedin_max_hashtags": 5,
    
    # Analytics
    "analytics_collection_interval_hours": 6,
    "analytics_retention_days": 365,
    "high_performance_threshold_views": 1000,
    "high_performance_threshold_read_ratio": 0.5,
    
    # Notificações
    "email_notifications_enabled": True,
    "email_weekly_reports": True,
    "email_high_performance_alerts": True,
    "email_error_alerts": True,
    "weekly_report_day": "sunday",
    "weekly_report_time": "09:00",
    
    # RSS
    "rss_check_interval_minutes": 60,
    "rss_max_articles_per_check": 10,
    
    # Backup
    "backup_enabled": True,
    "backup_interval_hours": 24,
    "backup_retention_days": 30,
    
    # Dashboard
    "dashboard_default_period_days": 30,
    "dashboard_refresh_interval_minutes": 5,
    "dashboard_show_drafts": False,
    
    # Templates LinkedIn
    "linkedin_templates": {
        "default": "🚀 Novo artigo publicado!\n\n{title}\n\n{summary}\n\nLeia mais: {url}\n\n{hashtags}",
        "professional": "📝 Compartilhando insights sobre {title}\n\n{summary}\n\nConfira o artigo completo: {url}\n\n{hashtags}",
        "casual": "Acabei de publicar um novo artigo! 🎉\n\n{title}\n\n{summary}\n\nDá uma olhada: {url}\n\n{hashtags}",
        "question": "O que vocês acham sobre {title}?\n\n{summary}\n\nCompartilhei meus pensamentos aqui: {url}\n\n{hashtags}"
    },
    
    # Hashtags padrão
    "default_hashtags": [
        "#medium",
        "#blog",
        "#writing",
        "#content",
        "#tech"
    ]
}


def initialize_default_settings(db):
    """
    Inicializar configurações padrão
    
    Args:
        db: Sessão do banco
    """
    for key, value in DEFAULT_SETTINGS.items():
        existing = db.query(UserSettings).filter(UserSettings.key == key).first()
        if not existing:
            setting = UserSettings(key=key, value=value)
            db.add(setting)
    
    db.commit()
