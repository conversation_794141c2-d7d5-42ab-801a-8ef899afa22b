# =============================================================================
# MEDIUM ANALYTICS BOT - MODELO DE POSTS LINKEDIN
# =============================================================================

from sqlalchemy import Column, Integer, String, Text, DateTime, ForeignKey, Boolean
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from datetime import datetime
from typing import Optional
from enum import Enum

from ..core.database import Base


class PostStatus(str, Enum):
    """Status do post no LinkedIn"""
    DRAFT = "draft"
    SCHEDULED = "scheduled"
    PUBLISHED = "published"
    FAILED = "failed"
    CANCELLED = "cancelled"


class LinkedInPost(Base):
    """Modelo para posts do LinkedIn"""
    
    __tablename__ = "linkedin_posts"
    
    # =============================================================================
    # CAMPOS PRINCIPAIS
    # =============================================================================
    
    id = Column(Integer, primary_key=True, index=True)
    article_id = Column(Integer, ForeignKey("articles.id"), nullable=False, index=True)
    
    # LinkedIn
    linkedin_post_id = Column(String(255), unique=True, index=True)
    linkedin_urn = Column(String(500))  # URN completo do LinkedIn
    
    # Conteúdo
    content = Column(Text, nullable=False)
    template_used = Column(String(100))
    hashtags = Column(Text)  # Hashtags separadas por vírgula
    
    # Agendamento
    scheduled_at = Column(DateTime(timezone=True), index=True)
    published_at = Column(DateTime(timezone=True), index=True)
    
    # Status
    status = Column(String(50), default=PostStatus.DRAFT, index=True)
    error_message = Column(Text)
    retry_count = Column(Integer, default=0)
    
    # Configurações
    auto_generated = Column(Boolean, default=True)
    is_promoted = Column(Boolean, default=False)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    
    # =============================================================================
    # RELACIONAMENTOS
    # =============================================================================
    
    # Artigo relacionado
    article = relationship("Article", back_populates="linkedin_posts")
    
    # Métricas do post
    metrics = relationship("LinkedInMetrics", back_populates="linkedin_post", cascade="all, delete-orphan")
    
    # =============================================================================
    # PROPRIEDADES
    # =============================================================================
    
    @property
    def latest_metrics(self) -> Optional["LinkedInMetrics"]:
        """Obter as métricas mais recentes"""
        if self.metrics:
            return max(self.metrics, key=lambda m: m.collected_at)
        return None
    
    @property
    def total_impressions(self) -> int:
        """Total de impressões (última métrica)"""
        latest = self.latest_metrics
        return latest.impressions if latest else 0
    
    @property
    def total_clicks(self) -> int:
        """Total de cliques (última métrica)"""
        latest = self.latest_metrics
        return latest.clicks if latest else 0
    
    @property
    def total_likes(self) -> int:
        """Total de likes (última métrica)"""
        latest = self.latest_metrics
        return latest.likes if latest else 0
    
    @property
    def total_comments(self) -> int:
        """Total de comentários (última métrica)"""
        latest = self.latest_metrics
        return latest.comments if latest else 0
    
    @property
    def total_shares(self) -> int:
        """Total de compartilhamentos (última métrica)"""
        latest = self.latest_metrics
        return latest.shares if latest else 0
    
    @property
    def engagement_rate(self) -> float:
        """Taxa de engajamento"""
        if self.total_impressions == 0:
            return 0.0
        
        total_engagement = self.total_likes + self.total_comments + self.total_shares
        return total_engagement / self.total_impressions
    
    @property
    def click_through_rate(self) -> float:
        """Taxa de cliques"""
        if self.total_impressions == 0:
            return 0.0
        
        return self.total_clicks / self.total_impressions
    
    @property
    def is_published(self) -> bool:
        """Verificar se foi publicado"""
        return self.status == PostStatus.PUBLISHED
    
    @property
    def is_scheduled(self) -> bool:
        """Verificar se está agendado"""
        return self.status == PostStatus.SCHEDULED
    
    @property
    def is_failed(self) -> bool:
        """Verificar se falhou"""
        return self.status == PostStatus.FAILED
    
    @property
    def days_since_published(self) -> Optional[int]:
        """Dias desde a publicação"""
        if self.published_at:
            return (datetime.utcnow() - self.published_at).days
        return None
    
    # =============================================================================
    # MÉTODOS
    # =============================================================================
    
    def add_metrics(
        self,
        impressions: int,
        clicks: int,
        likes: int,
        comments: int,
        shares: int
    ) -> "LinkedInMetrics":
        """
        Adicionar nova métrica para o post
        
        Args:
            impressions: Número de impressões
            clicks: Número de cliques
            likes: Número de likes
            comments: Número de comentários
            shares: Número de compartilhamentos
            
        Returns:
            Nova instância de LinkedInMetrics
        """
        metric = LinkedInMetrics(
            linkedin_post_id=self.id,
            impressions=impressions,
            clicks=clicks,
            likes=likes,
            comments=comments,
            shares=shares
        )
        
        self.metrics.append(metric)
        return metric
    
    def mark_as_published(self, linkedin_post_id: str, linkedin_urn: str = None):
        """Marcar como publicado"""
        self.status = PostStatus.PUBLISHED
        self.linkedin_post_id = linkedin_post_id
        self.linkedin_urn = linkedin_urn
        self.published_at = datetime.utcnow()
        self.error_message = None
    
    def mark_as_failed(self, error_message: str):
        """Marcar como falhou"""
        self.status = PostStatus.FAILED
        self.error_message = error_message
        self.retry_count += 1
    
    def mark_as_scheduled(self, scheduled_at: datetime):
        """Marcar como agendado"""
        self.status = PostStatus.SCHEDULED
        self.scheduled_at = scheduled_at
    
    def get_hashtags_list(self) -> list:
        """Obter lista de hashtags"""
        if not self.hashtags:
            return []
        return [tag.strip() for tag in self.hashtags.split(",") if tag.strip()]
    
    def set_hashtags_from_list(self, hashtags: list):
        """Definir hashtags a partir de lista"""
        self.hashtags = ", ".join(hashtags) if hashtags else None
    
    def to_dict(self) -> dict:
        """Converter para dicionário"""
        return {
            "id": self.id,
            "article_id": self.article_id,
            "linkedin_post_id": self.linkedin_post_id,
            "linkedin_urn": self.linkedin_urn,
            "content": self.content,
            "template_used": self.template_used,
            "hashtags": self.get_hashtags_list(),
            "scheduled_at": self.scheduled_at.isoformat() if self.scheduled_at else None,
            "published_at": self.published_at.isoformat() if self.published_at else None,
            "status": self.status,
            "error_message": self.error_message,
            "retry_count": self.retry_count,
            "auto_generated": self.auto_generated,
            "is_promoted": self.is_promoted,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
            "total_impressions": self.total_impressions,
            "total_clicks": self.total_clicks,
            "total_likes": self.total_likes,
            "total_comments": self.total_comments,
            "total_shares": self.total_shares,
            "engagement_rate": self.engagement_rate,
            "click_through_rate": self.click_through_rate,
            "days_since_published": self.days_since_published
        }
    
    def __repr__(self):
        return f"<LinkedInPost(id={self.id}, article_id={self.article_id}, status='{self.status}')>"


class LinkedInMetrics(Base):
    """Modelo para métricas dos posts do LinkedIn"""
    
    __tablename__ = "linkedin_metrics"
    
    # =============================================================================
    # CAMPOS PRINCIPAIS
    # =============================================================================
    
    id = Column(Integer, primary_key=True, index=True)
    linkedin_post_id = Column(Integer, ForeignKey("linkedin_posts.id"), nullable=False, index=True)
    
    # Métricas
    impressions = Column(Integer, default=0)
    clicks = Column(Integer, default=0)
    likes = Column(Integer, default=0)
    comments = Column(Integer, default=0)
    shares = Column(Integer, default=0)
    
    # Timestamp
    collected_at = Column(DateTime(timezone=True), server_default=func.now(), index=True)
    
    # =============================================================================
    # RELACIONAMENTOS
    # =============================================================================
    
    linkedin_post = relationship("LinkedInPost", back_populates="metrics")
    
    # =============================================================================
    # MÉTODOS
    # =============================================================================
    
    def to_dict(self) -> dict:
        """Converter para dicionário"""
        return {
            "id": self.id,
            "linkedin_post_id": self.linkedin_post_id,
            "impressions": self.impressions,
            "clicks": self.clicks,
            "likes": self.likes,
            "comments": self.comments,
            "shares": self.shares,
            "collected_at": self.collected_at.isoformat() if self.collected_at else None
        }
    
    def __repr__(self):
        return f"<LinkedInMetrics(post_id={self.linkedin_post_id}, impressions={self.impressions})>"
