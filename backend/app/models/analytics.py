# =============================================================================
# MEDIUM ANALYTICS BOT - MODELO DE ANALYTICS AGREGADOS
# =============================================================================

from sqlalchemy import Column, Integer, Date, Numeric, DateTime, UniqueConstraint
from sqlalchemy.sql import func
from datetime import datetime, date
from typing import Optional

from ..core.database import Base


class AnalyticsSummary(Base):
    """Modelo para analytics agregados por dia"""
    
    __tablename__ = "analytics_summary"
    
    # =============================================================================
    # CAMPOS PRINCIPAIS
    # =============================================================================
    
    id = Column(Integer, primary_key=True, index=True)
    date = Column(Date, nullable=False, index=True)
    
    # Métricas agregadas
    total_views = Column(Integer, default=0)
    total_reads = Column(Integer, default=0)
    total_claps = Column(Integer, default=0)
    total_responses = Column(Integer, default=0)
    
    # Contadores
    articles_published = Column(Integer, default=0)
    linkedin_posts = Column(Integer, default=0)
    
    # Médias
    avg_read_ratio = Column(Numeric(5, 4))
    avg_engagement_score = Column(Numeric(8, 4))
    
    # LinkedIn
    linkedin_impressions = Column(Integer, default=0)
    linkedin_clicks = Column(Integer, default=0)
    linkedin_likes = Column(Integer, default=0)
    linkedin_comments = Column(Integer, default=0)
    linkedin_shares = Column(Integer, default=0)
    
    # Timestamp
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    
    # =============================================================================
    # CONSTRAINTS
    # =============================================================================
    
    __table_args__ = (
        UniqueConstraint('date', name='uq_analytics_summary_date'),
    )
    
    # =============================================================================
    # PROPRIEDADES
    # =============================================================================
    
    @property
    def overall_read_ratio(self) -> float:
        """Taxa de leitura geral"""
        if self.total_views == 0:
            return 0.0
        return self.total_reads / self.total_views
    
    @property
    def linkedin_engagement_rate(self) -> float:
        """Taxa de engajamento do LinkedIn"""
        if self.linkedin_impressions == 0:
            return 0.0
        
        total_engagement = self.linkedin_likes + self.linkedin_comments + self.linkedin_shares
        return total_engagement / self.linkedin_impressions
    
    @property
    def linkedin_ctr(self) -> float:
        """Click-through rate do LinkedIn"""
        if self.linkedin_impressions == 0:
            return 0.0
        return self.linkedin_clicks / self.linkedin_impressions
    
    @property
    def articles_per_linkedin_post(self) -> float:
        """Proporção artigos/posts LinkedIn"""
        if self.linkedin_posts == 0:
            return 0.0
        return self.articles_published / self.linkedin_posts
    
    # =============================================================================
    # MÉTODOS
    # =============================================================================
    
    def to_dict(self) -> dict:
        """Converter para dicionário"""
        return {
            "id": self.id,
            "date": self.date.isoformat() if self.date else None,
            "total_views": self.total_views,
            "total_reads": self.total_reads,
            "total_claps": self.total_claps,
            "total_responses": self.total_responses,
            "articles_published": self.articles_published,
            "linkedin_posts": self.linkedin_posts,
            "avg_read_ratio": float(self.avg_read_ratio) if self.avg_read_ratio else 0.0,
            "avg_engagement_score": float(self.avg_engagement_score) if self.avg_engagement_score else 0.0,
            "linkedin_impressions": self.linkedin_impressions,
            "linkedin_clicks": self.linkedin_clicks,
            "linkedin_likes": self.linkedin_likes,
            "linkedin_comments": self.linkedin_comments,
            "linkedin_shares": self.linkedin_shares,
            "overall_read_ratio": self.overall_read_ratio,
            "linkedin_engagement_rate": self.linkedin_engagement_rate,
            "linkedin_ctr": self.linkedin_ctr,
            "articles_per_linkedin_post": self.articles_per_linkedin_post,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None
        }
    
    @classmethod
    def get_or_create_for_date(cls, db, target_date: date) -> "AnalyticsSummary":
        """
        Obter ou criar summary para uma data
        
        Args:
            db: Sessão do banco
            target_date: Data alvo
            
        Returns:
            Instância do AnalyticsSummary
        """
        summary = db.query(cls).filter(cls.date == target_date).first()
        
        if not summary:
            summary = cls(date=target_date)
            db.add(summary)
            db.commit()
            db.refresh(summary)
        
        return summary
    
    @classmethod
    def update_daily_summary(cls, db, target_date: date = None) -> "AnalyticsSummary":
        """
        Atualizar summary diário com dados atuais
        
        Args:
            db: Sessão do banco
            target_date: Data alvo (padrão: hoje)
            
        Returns:
            Summary atualizado
        """
        if target_date is None:
            target_date = date.today()
        
        summary = cls.get_or_create_for_date(db, target_date)
        
        # Importar modelos aqui para evitar import circular
        from .article import Article, ArticleMetrics
        from .linkedin_post import LinkedInPost, LinkedInMetrics
        
        # Agregar métricas de artigos
        article_metrics = db.query(
            func.sum(ArticleMetrics.views).label('total_views'),
            func.sum(ArticleMetrics.reads).label('total_reads'),
            func.sum(ArticleMetrics.claps).label('total_claps'),
            func.sum(ArticleMetrics.responses).label('total_responses'),
            func.avg(ArticleMetrics.read_ratio).label('avg_read_ratio')
        ).join(Article).filter(
            func.date(Article.published_at) == target_date
        ).first()
        
        # Contar artigos publicados
        articles_count = db.query(Article).filter(
            func.date(Article.published_at) == target_date
        ).count()
        
        # Agregar métricas do LinkedIn
        linkedin_metrics = db.query(
            func.sum(LinkedInMetrics.impressions).label('total_impressions'),
            func.sum(LinkedInMetrics.clicks).label('total_clicks'),
            func.sum(LinkedInMetrics.likes).label('total_likes'),
            func.sum(LinkedInMetrics.comments).label('total_comments'),
            func.sum(LinkedInMetrics.shares).label('total_shares')
        ).join(LinkedInPost).filter(
            func.date(LinkedInPost.published_at) == target_date
        ).first()
        
        # Contar posts do LinkedIn
        linkedin_posts_count = db.query(LinkedInPost).filter(
            func.date(LinkedInPost.published_at) == target_date
        ).count()
        
        # Atualizar summary
        if article_metrics:
            summary.total_views = article_metrics.total_views or 0
            summary.total_reads = article_metrics.total_reads or 0
            summary.total_claps = article_metrics.total_claps or 0
            summary.total_responses = article_metrics.total_responses or 0
            summary.avg_read_ratio = article_metrics.avg_read_ratio
        
        summary.articles_published = articles_count
        
        if linkedin_metrics:
            summary.linkedin_impressions = linkedin_metrics.total_impressions or 0
            summary.linkedin_clicks = linkedin_metrics.total_clicks or 0
            summary.linkedin_likes = linkedin_metrics.total_likes or 0
            summary.linkedin_comments = linkedin_metrics.total_comments or 0
            summary.linkedin_shares = linkedin_metrics.total_shares or 0
        
        summary.linkedin_posts = linkedin_posts_count
        
        # Calcular engagement score médio
        if summary.total_views > 0:
            summary.avg_engagement_score = (
                (summary.total_reads * 2 + summary.total_claps * 3) / summary.total_views
            )
        
        db.commit()
        db.refresh(summary)
        
        return summary
    
    @classmethod
    def get_period_summary(cls, db, start_date: date, end_date: date) -> dict:
        """
        Obter summary agregado para um período
        
        Args:
            db: Sessão do banco
            start_date: Data inicial
            end_date: Data final
            
        Returns:
            Dicionário com métricas agregadas
        """
        summaries = db.query(cls).filter(
            cls.date >= start_date,
            cls.date <= end_date
        ).all()
        
        if not summaries:
            return {
                "total_views": 0,
                "total_reads": 0,
                "total_claps": 0,
                "total_responses": 0,
                "articles_published": 0,
                "linkedin_posts": 0,
                "avg_read_ratio": 0.0,
                "linkedin_impressions": 0,
                "linkedin_clicks": 0,
                "linkedin_engagement": 0,
                "days": 0
            }
        
        total_views = sum(s.total_views for s in summaries)
        total_reads = sum(s.total_reads for s in summaries)
        total_claps = sum(s.total_claps for s in summaries)
        total_responses = sum(s.total_responses for s in summaries)
        articles_published = sum(s.articles_published for s in summaries)
        linkedin_posts = sum(s.linkedin_posts for s in summaries)
        linkedin_impressions = sum(s.linkedin_impressions for s in summaries)
        linkedin_clicks = sum(s.linkedin_clicks for s in summaries)
        linkedin_engagement = sum(
            s.linkedin_likes + s.linkedin_comments + s.linkedin_shares 
            for s in summaries
        )
        
        # Calcular médias
        avg_read_ratio = total_reads / total_views if total_views > 0 else 0.0
        
        return {
            "total_views": total_views,
            "total_reads": total_reads,
            "total_claps": total_claps,
            "total_responses": total_responses,
            "articles_published": articles_published,
            "linkedin_posts": linkedin_posts,
            "avg_read_ratio": avg_read_ratio,
            "linkedin_impressions": linkedin_impressions,
            "linkedin_clicks": linkedin_clicks,
            "linkedin_engagement": linkedin_engagement,
            "days": len(summaries)
        }
    
    def __repr__(self):
        return f"<AnalyticsSummary(date={self.date}, views={self.total_views}, articles={self.articles_published})>"
