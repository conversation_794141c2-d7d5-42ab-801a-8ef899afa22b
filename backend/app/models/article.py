# =============================================================================
# MEDIUM ANALYTICS BOT - MODELO DE ARTIGOS
# =============================================================================

from sqlalchemy import Column, Integer, String, Text, DateTime, ARRAY, ForeignKey, Numeric, Boolean
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from datetime import datetime, timedelta
from typing import List, Optional

from ..core.database import Base


class Article(Base):
    """Modelo para artigos do Medium"""
    
    __tablename__ = "articles"
    
    # =============================================================================
    # CAMPOS PRINCIPAIS
    # =============================================================================
    
    id = Column(Integer, primary_key=True, index=True)
    guid = Column(String(255), unique=True, nullable=False, index=True)
    title = Column(String(500), nullable=False)
    url = Column(String(500), nullable=False)
    
    # Conteúdo
    content = Column(Text)
    summary = Column(Text)
    
    # Metadados
    published_at = Column(DateTime(timezone=True), nullable=False, index=True)
    reading_time = Column(Integer)  # em minutos
    word_count = Column(Integer)
    
    # Tags e categorias
    tags = Column(ARRAY(String))
    categories = Column(ARRAY(String))
    
    # Status
    is_active = Column(Boolean, default=True)
    is_featured = Column(Boolean, default=False)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    
    # =============================================================================
    # RELACIONAMENTOS
    # =============================================================================
    
    # Métricas do artigo
    metrics = relationship("ArticleMetrics", back_populates="article", cascade="all, delete-orphan")
    
    # Posts do LinkedIn relacionados
    linkedin_posts = relationship("LinkedInPost", back_populates="article", cascade="all, delete-orphan")
    
    # =============================================================================
    # PROPRIEDADES
    # =============================================================================
    
    @property
    def latest_metrics(self) -> Optional["ArticleMetrics"]:
        """Obter as métricas mais recentes"""
        if self.metrics:
            return max(self.metrics, key=lambda m: m.collected_at)
        return None
    
    @property
    def total_views(self) -> int:
        """Total de views (última métrica)"""
        latest = self.latest_metrics
        return latest.views if latest else 0
    
    @property
    def total_reads(self) -> int:
        """Total de reads (última métrica)"""
        latest = self.latest_metrics
        return latest.reads if latest else 0
    
    @property
    def total_claps(self) -> int:
        """Total de claps (última métrica)"""
        latest = self.latest_metrics
        return latest.claps if latest else 0
    
    @property
    def read_ratio(self) -> float:
        """Taxa de leitura (última métrica)"""
        latest = self.latest_metrics
        return float(latest.read_ratio) if latest and latest.read_ratio else 0.0
    
    @property
    def engagement_score(self) -> float:
        """Score de engajamento calculado"""
        if self.total_views == 0:
            return 0.0
        
        # Fórmula: (reads * 2 + claps * 3) / views
        return (self.total_reads * 2 + self.total_claps * 3) / self.total_views
    
    @property
    def is_high_performance(self) -> bool:
        """Verificar se é um artigo de alta performance"""
        from ..core.config import settings
        return (
            self.total_views >= settings.HIGH_PERFORMANCE_VIEWS_THRESHOLD or
            self.read_ratio >= settings.HIGH_PERFORMANCE_READ_RATIO_THRESHOLD
        )
    
    @property
    def days_since_published(self) -> int:
        """Dias desde a publicação"""
        from datetime import timezone
        now = datetime.now(timezone.utc)
        if self.published_at.tzinfo is None:
            # Se published_at não tem timezone, assumir UTC
            published_at = self.published_at.replace(tzinfo=timezone.utc)
        else:
            published_at = self.published_at
        return (now - published_at).days
    
    # =============================================================================
    # MÉTODOS
    # =============================================================================
    
    def add_metrics(
        self,
        views: int,
        reads: int,
        claps: int,
        responses: int = 0,
        fans: int = 0
    ) -> "ArticleMetrics":
        """
        Adicionar nova métrica para o artigo
        
        Args:
            views: Número de visualizações
            reads: Número de leituras
            claps: Número de aplausos
            responses: Número de respostas
            fans: Número de fãs
            
        Returns:
            Nova instância de ArticleMetrics
        """
        read_ratio = reads / views if views > 0 else 0
        
        metric = ArticleMetrics(
            article_id=self.id,
            views=views,
            reads=reads,
            claps=claps,
            responses=responses,
            fans=fans,
            read_ratio=read_ratio
        )
        
        self.metrics.append(metric)
        return metric
    
    def get_metrics_history(self, days: int = 30) -> List["ArticleMetrics"]:
        """
        Obter histórico de métricas
        
        Args:
            days: Número de dias para buscar
            
        Returns:
            Lista de métricas ordenadas por data
        """
        cutoff_date = datetime.utcnow() - timedelta(days=days)
        return [
            m for m in self.metrics 
            if m.collected_at >= cutoff_date
        ]
    
    def to_dict(self) -> dict:
        """Converter para dicionário"""
        return {
            "id": self.id,
            "guid": self.guid,
            "title": self.title,
            "url": self.url,
            "content": self.content,
            "summary": self.summary,
            "published_at": self.published_at.isoformat() if self.published_at else None,
            "reading_time": self.reading_time,
            "word_count": self.word_count,
            "tags": self.tags or [],
            "categories": self.categories or [],
            "is_active": self.is_active,
            "is_featured": self.is_featured,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
            "total_views": self.total_views,
            "total_reads": self.total_reads,
            "total_claps": self.total_claps,
            "read_ratio": self.read_ratio,
            "engagement_score": self.engagement_score,
            "is_high_performance": self.is_high_performance,
            "days_since_published": self.days_since_published
        }
    
    def __repr__(self):
        return f"<Article(id={self.id}, title='{self.title[:50]}...', views={self.total_views})>"


class ArticleMetrics(Base):
    """Modelo para métricas dos artigos"""
    
    __tablename__ = "article_metrics"
    
    # =============================================================================
    # CAMPOS PRINCIPAIS
    # =============================================================================
    
    id = Column(Integer, primary_key=True, index=True)
    article_id = Column(Integer, ForeignKey("articles.id"), nullable=False, index=True)
    
    # Métricas
    views = Column(Integer, default=0)
    reads = Column(Integer, default=0)
    claps = Column(Integer, default=0)
    responses = Column(Integer, default=0)
    fans = Column(Integer, default=0)
    
    # Calculadas
    read_ratio = Column(Numeric(5, 4))  # Precisão de 4 casas decimais
    
    # Timestamp
    collected_at = Column(DateTime(timezone=True), server_default=func.now(), index=True)
    
    # =============================================================================
    # RELACIONAMENTOS
    # =============================================================================
    
    article = relationship("Article", back_populates="metrics")
    
    # =============================================================================
    # MÉTODOS
    # =============================================================================
    
    def to_dict(self) -> dict:
        """Converter para dicionário"""
        return {
            "id": self.id,
            "article_id": self.article_id,
            "views": self.views,
            "reads": self.reads,
            "claps": self.claps,
            "responses": self.responses,
            "fans": self.fans,
            "read_ratio": float(self.read_ratio) if self.read_ratio else 0.0,
            "collected_at": self.collected_at.isoformat() if self.collected_at else None
        }
    
    def __repr__(self):
        return f"<ArticleMetrics(article_id={self.article_id}, views={self.views}, collected_at={self.collected_at})>"
