# =============================================================================
# MEDIUM ANALYTICS BOT - SERVIÇO DO MEDIUM
# =============================================================================

import feedparser
import requests
from bs4 import BeautifulSoup
from datetime import datetime
from typing import List, Optional, Dict, Any
import logging
import re
from urllib.parse import urlparse

from sqlalchemy.orm import Session
from ..models.article import Article, ArticleMetrics
from ..core.config import settings
from ..core.cache import cache

logger = logging.getLogger(__name__)


class MediumService:
    """Serviço para interagir com o Medium"""
    
    def __init__(self, db: Session):
        self.db = db
        self.rss_url = settings.MEDIUM_RSS_URL
        
    async def sync_articles(self) -> List[Article]:
        """
        Sincronizar artigos do RSS do Medium
        
        Returns:
            Lista de novos artigos encontrados
        """
        if not self.rss_url:
            logger.warning("URL do RSS do Medium não configurada")
            return []
        
        try:
            logger.info(f"Sincronizando artigos do RSS: {self.rss_url}")
            
            # Buscar RSS
            rss_data = await self._fetch_rss()
            if not rss_data:
                return []
            
            # Processar entradas do RSS
            new_articles = []
            for entry in rss_data.entries:
                try:
                    article = await self._process_rss_entry(entry)
                    if article:
                        new_articles.append(article)
                except Exception as e:
                    logger.error(f"Erro ao processar entrada RSS: {e}")
                    continue
            
            logger.info(f"Sincronização concluída. {len(new_articles)} novos artigos encontrados.")
            return new_articles
            
        except Exception as e:
            logger.error(f"Erro na sincronização do RSS: {e}")
            raise
    
    async def _fetch_rss(self) -> Optional[feedparser.FeedParserDict]:
        """
        Buscar dados do RSS
        
        Returns:
            Dados do RSS parseados
        """
        try:
            # Verificar cache primeiro
            cache_key = f"medium_rss:{self.rss_url}"
            cached_data = cache.get(cache_key)
            
            if cached_data:
                logger.info("Usando dados do RSS em cache")
                return feedparser.parse(cached_data)
            
            # Buscar RSS
            headers = {
                'User-Agent': 'Medium Analytics Bot/1.0 (https://github.com/your-repo)'
            }
            
            response = requests.get(self.rss_url, headers=headers, timeout=30)
            response.raise_for_status()
            
            # Cachear por 30 minutos
            cache.set(cache_key, response.text, ttl=1800)
            
            # Parsear RSS
            rss_data = feedparser.parse(response.text)
            
            if rss_data.bozo:
                logger.warning(f"RSS pode ter problemas: {rss_data.bozo_exception}")
            
            logger.info(f"RSS carregado com sucesso. {len(rss_data.entries)} entradas encontradas.")
            return rss_data
            
        except requests.RequestException as e:
            logger.error(f"Erro ao buscar RSS: {e}")
            return None
        except Exception as e:
            logger.error(f"Erro ao processar RSS: {e}")
            return None
    
    async def _process_rss_entry(self, entry) -> Optional[Article]:
        """
        Processar uma entrada do RSS
        
        Args:
            entry: Entrada do feedparser
            
        Returns:
            Artigo criado ou None se já existe
        """
        try:
            # Verificar se artigo já existe
            existing = self.db.query(Article).filter(
                Article.guid == entry.id
            ).first()
            
            if existing:
                logger.debug(f"Artigo já existe: {entry.title}")
                return None
            
            # Extrair dados da entrada
            article_data = self._extract_article_data(entry)
            
            # Criar novo artigo
            article = Article(**article_data)
            self.db.add(article)
            self.db.commit()
            self.db.refresh(article)
            
            logger.info(f"Novo artigo criado: {article.title}")
            
            # Tentar extrair métricas iniciais
            await self._extract_initial_metrics(article)
            
            return article
            
        except Exception as e:
            logger.error(f"Erro ao processar entrada RSS: {e}")
            self.db.rollback()
            return None
    
    def _extract_article_data(self, entry) -> Dict[str, Any]:
        """
        Extrair dados do artigo da entrada RSS
        
        Args:
            entry: Entrada do feedparser
            
        Returns:
            Dicionário com dados do artigo
        """
        # Data de publicação
        published_at = datetime.now()
        if hasattr(entry, 'published_parsed') and entry.published_parsed:
            published_at = datetime(*entry.published_parsed[:6])
        elif hasattr(entry, 'updated_parsed') and entry.updated_parsed:
            published_at = datetime(*entry.updated_parsed[:6])
        
        # Conteúdo
        content = ""
        summary = ""
        
        if hasattr(entry, 'content') and entry.content:
            content = entry.content[0].value if entry.content else ""
        elif hasattr(entry, 'summary'):
            content = entry.summary
        
        # Limpar HTML do conteúdo
        if content:
            soup = BeautifulSoup(content, 'html.parser')
            summary = soup.get_text()[:500] + "..." if len(soup.get_text()) > 500 else soup.get_text()
            content = soup.get_text()
        
        # Tags
        tags = []
        if hasattr(entry, 'tags') and entry.tags:
            tags = [tag.term for tag in entry.tags if hasattr(tag, 'term')]
        
        # Categorias
        categories = []
        if hasattr(entry, 'category'):
            categories = [entry.category] if isinstance(entry.category, str) else entry.category
        
        # Tempo de leitura estimado
        word_count = len(content.split()) if content else 0
        reading_time = max(1, round(word_count / 200))  # 200 palavras por minuto
        
        return {
            'guid': entry.id,
            'title': entry.title,
            'url': entry.link,
            'content': content,
            'summary': summary,
            'published_at': published_at,
            'reading_time': reading_time,
            'word_count': word_count,
            'tags': tags if tags else None,
            'categories': categories if categories else None,
            'is_active': True
        }
    
    async def _extract_initial_metrics(self, article: Article):
        """
        Extrair métricas iniciais do artigo (se possível)
        
        Args:
            article: Artigo para extrair métricas
        """
        try:
            # Por enquanto, criar métricas iniciais zeradas
            # Em uma implementação real, você poderia tentar extrair
            # métricas da página do Medium usando web scraping
            
            initial_metrics = ArticleMetrics(
                article_id=article.id,
                views=0,
                reads=0,
                claps=0,
                responses=0,
                fans=0,
                read_ratio=0.0
            )
            
            self.db.add(initial_metrics)
            self.db.commit()
            
            logger.debug(f"Métricas iniciais criadas para: {article.title}")
            
        except Exception as e:
            logger.error(f"Erro ao criar métricas iniciais: {e}")
    
    async def update_article_metrics(self, article: Article, metrics_data: Dict[str, int]) -> ArticleMetrics:
        """
        Atualizar métricas de um artigo
        
        Args:
            article: Artigo a ser atualizado
            metrics_data: Dados das métricas
            
        Returns:
            Nova instância de métricas
        """
        try:
            views = metrics_data.get('views', 0)
            reads = metrics_data.get('reads', 0)
            claps = metrics_data.get('claps', 0)
            responses = metrics_data.get('responses', 0)
            fans = metrics_data.get('fans', 0)
            
            # Calcular taxa de leitura
            read_ratio = reads / views if views > 0 else 0.0
            
            # Criar nova métrica
            metrics = ArticleMetrics(
                article_id=article.id,
                views=views,
                reads=reads,
                claps=claps,
                responses=responses,
                fans=fans,
                read_ratio=read_ratio
            )
            
            self.db.add(metrics)
            self.db.commit()
            self.db.refresh(metrics)
            
            logger.info(f"Métricas atualizadas para: {article.title} - Views: {views}, Reads: {reads}")
            
            return metrics
            
        except Exception as e:
            logger.error(f"Erro ao atualizar métricas: {e}")
            self.db.rollback()
            raise
    
    def get_article_by_url(self, url: str) -> Optional[Article]:
        """
        Buscar artigo por URL
        
        Args:
            url: URL do artigo
            
        Returns:
            Artigo encontrado ou None
        """
        return self.db.query(Article).filter(Article.url == url).first()
    
    def get_recent_articles(self, limit: int = 10) -> List[Article]:
        """
        Obter artigos mais recentes
        
        Args:
            limit: Número máximo de artigos
            
        Returns:
            Lista de artigos recentes
        """
        return self.db.query(Article).order_by(
            Article.published_at.desc()
        ).limit(limit).all()
    
    def get_popular_articles(self, limit: int = 10) -> List[Article]:
        """
        Obter artigos mais populares (por views)
        
        Args:
            limit: Número máximo de artigos
            
        Returns:
            Lista de artigos populares
        """
        # Subconsulta para obter as métricas mais recentes
        from sqlalchemy import func
        from ..models.article import ArticleMetrics
        
        latest_metrics = self.db.query(
            ArticleMetrics.article_id,
            func.max(ArticleMetrics.collected_at).label('latest_date')
        ).group_by(ArticleMetrics.article_id).subquery()
        
        # Query principal
        articles = self.db.query(Article).join(
            ArticleMetrics, Article.id == ArticleMetrics.article_id
        ).join(
            latest_metrics,
            (ArticleMetrics.article_id == latest_metrics.c.article_id) &
            (ArticleMetrics.collected_at == latest_metrics.c.latest_date)
        ).order_by(
            ArticleMetrics.views.desc()
        ).limit(limit).all()
        
        return articles
    
    async def simulate_metrics_update(self, article_id: int) -> Optional[ArticleMetrics]:
        """
        Simular atualização de métricas (para desenvolvimento/teste)
        
        Args:
            article_id: ID do artigo
            
        Returns:
            Métricas simuladas
        """
        import random
        
        article = self.db.query(Article).filter(Article.id == article_id).first()
        if not article:
            return None
        
        # Obter métricas anteriores
        latest_metrics = article.latest_metrics
        base_views = latest_metrics.views if latest_metrics else 0
        base_reads = latest_metrics.reads if latest_metrics else 0
        base_claps = latest_metrics.claps if latest_metrics else 0
        
        # Simular crescimento
        new_views = base_views + random.randint(10, 100)
        new_reads = base_reads + random.randint(5, 50)
        new_claps = base_claps + random.randint(0, 20)
        new_responses = random.randint(0, 5)
        new_fans = random.randint(0, 10)
        
        metrics_data = {
            'views': new_views,
            'reads': new_reads,
            'claps': new_claps,
            'responses': new_responses,
            'fans': new_fans
        }
        
        return await self.update_article_metrics(article, metrics_data)
    
    def validate_rss_url(self, url: str) -> bool:
        """
        Validar URL do RSS
        
        Args:
            url: URL a ser validada
            
        Returns:
            True se válida
        """
        try:
            parsed = urlparse(url)
            if not parsed.scheme or not parsed.netloc:
                return False
            
            # Verificar se é uma URL do Medium
            if 'medium.com' not in parsed.netloc:
                logger.warning(f"URL não é do Medium: {url}")
            
            return True
            
        except Exception:
            return False
