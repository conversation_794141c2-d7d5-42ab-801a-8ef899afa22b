# =============================================================================
# MEDIUM ANALYTICS BOT - APLICAÇÃO PRINCIPAL
# =============================================================================

from fastapi import FastAPI, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.responses import JSONResponse
from contextlib import asynccontextmanager
import logging
import time

from .core.config import settings, validate_required_settings
from .core.database import init_db, health_check as db_health_check
from .core.cache import redis_health_check
from .api.v1 import api_router


# =============================================================================
# CONFIGURAÇÃO DE LOGGING
# =============================================================================

logging.basicConfig(
    level=getattr(logging, settings.LOG_LEVEL),
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)

logger = logging.getLogger(__name__)


# =============================================================================
# LIFESPAN EVENTS
# =============================================================================

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Eventos de inicialização e finalização"""
    
    # Startup
    logger.info("🚀 Iniciando Medium Analytics Bot...")
    
    try:
        # Validar configurações
        validate_required_settings()
        logger.info("✅ Configurações validadas")
        
        # Inicializar banco de dados
        init_db()
        logger.info("✅ Banco de dados inicializado")
        
        # Verificar conexões
        db_status = await db_health_check()
        redis_status = await redis_health_check()
        
        if db_status["status"] != "healthy":
            logger.error(f"❌ Banco de dados não está saudável: {db_status}")
        else:
            logger.info("✅ Banco de dados conectado")
            
        if redis_status["status"] != "healthy":
            logger.error(f"❌ Redis não está saudável: {redis_status}")
        else:
            logger.info("✅ Redis conectado")
        
        logger.info("🎉 Aplicação iniciada com sucesso!")
        
    except Exception as e:
        logger.error(f"❌ Erro na inicialização: {e}")
        raise
    
    yield
    
    # Shutdown
    logger.info("🛑 Finalizando aplicação...")


# =============================================================================
# APLICAÇÃO FASTAPI
# =============================================================================

app = FastAPI(
    title=settings.PROJECT_NAME,
    version=settings.VERSION,
    description=settings.DESCRIPTION,
    openapi_url=f"{settings.API_PREFIX}/openapi.json",
    docs_url="/docs",
    redoc_url="/redoc",
    lifespan=lifespan
)

# =============================================================================
# MIDDLEWARES
# =============================================================================

# CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.CORS_ORIGINS,
    allow_credentials=settings.CORS_ALLOW_CREDENTIALS,
    allow_methods=settings.CORS_ALLOW_METHODS,
    allow_headers=settings.CORS_ALLOW_HEADERS,
)

# Trusted Host (para produção)
if settings.ENVIRONMENT == "production":
    app.add_middleware(
        TrustedHostMiddleware,
        allowed_hosts=["*"]  # Configurar hosts específicos em produção
    )


# Middleware de timing
@app.middleware("http")
async def add_process_time_header(request: Request, call_next):
    """Adicionar tempo de processamento no header"""
    start_time = time.time()
    response = await call_next(request)
    process_time = time.time() - start_time
    response.headers["X-Process-Time"] = str(process_time)
    return response


# Middleware de logging
@app.middleware("http")
async def log_requests(request: Request, call_next):
    """Log de requisições"""
    start_time = time.time()
    
    # Log da requisição
    logger.info(f"📥 {request.method} {request.url.path} - {request.client.host}")
    
    response = await call_next(request)
    
    # Log da resposta
    process_time = time.time() - start_time
    logger.info(
        f"📤 {request.method} {request.url.path} - "
        f"Status: {response.status_code} - "
        f"Time: {process_time:.4f}s"
    )
    
    return response


# =============================================================================
# EXCEPTION HANDLERS
# =============================================================================

@app.exception_handler(Exception)
async def global_exception_handler(request: Request, exc: Exception):
    """Handler global para exceções"""
    logger.error(f"❌ Erro não tratado: {exc}", exc_info=True)
    
    return JSONResponse(
        status_code=500,
        content={
            "error": "Internal Server Error",
            "message": "Ocorreu um erro interno no servidor",
            "detail": str(exc) if settings.DEBUG else None
        }
    )


# =============================================================================
# ROTAS PRINCIPAIS
# =============================================================================

@app.get("/")
async def root():
    """Rota raiz"""
    return {
        "message": "Medium Analytics Bot API",
        "version": settings.VERSION,
        "environment": settings.ENVIRONMENT,
        "docs": "/docs"
    }


@app.get("/health")
async def health_check():
    """Health check da aplicação"""
    try:
        # Verificar banco de dados
        db_status = await db_health_check()
        
        # Verificar Redis
        redis_status = await redis_health_check()
        
        # Status geral
        overall_status = "healthy"
        if db_status["status"] != "healthy" or redis_status["status"] != "healthy":
            overall_status = "unhealthy"
        
        return {
            "status": overall_status,
            "timestamp": time.time(),
            "version": settings.VERSION,
            "environment": settings.ENVIRONMENT,
            "services": {
                "database": db_status,
                "redis": redis_status
            }
        }
        
    except Exception as e:
        logger.error(f"Erro no health check: {e}")
        return JSONResponse(
            status_code=503,
            content={
                "status": "error",
                "timestamp": time.time(),
                "error": str(e)
            }
        )


@app.get("/info")
async def app_info():
    """Informações da aplicação"""
    return {
        "name": settings.PROJECT_NAME,
        "version": settings.VERSION,
        "description": settings.DESCRIPTION,
        "environment": settings.ENVIRONMENT,
        "debug": settings.DEBUG,
        "api_prefix": settings.API_PREFIX,
        "features": {
            "medium_rss": bool(settings.MEDIUM_RSS_URL),
            "linkedin_api": bool(settings.LINKEDIN_CLIENT_ID),
            "aws_integration": bool(settings.AWS_ACCESS_KEY_ID),
            "email_notifications": bool(settings.FROM_EMAIL)
        }
    }


# =============================================================================
# INCLUIR ROUTERS
# =============================================================================

# API v1
app.include_router(api_router, prefix=settings.API_PREFIX)


# =============================================================================
# STARTUP MESSAGE
# =============================================================================

if __name__ == "__main__":
    import uvicorn
    
    logger.info(f"🚀 Iniciando servidor em {settings.API_HOST}:{settings.API_PORT}")
    
    uvicorn.run(
        "app.main:app",
        host=settings.API_HOST,
        port=settings.API_PORT,
        reload=settings.DEBUG,
        log_level=settings.LOG_LEVEL.lower()
    )
