# =============================================================================
# MEDIUM ANALYTICS BOT - CONFIGURAÇÕES PRINCIPAIS
# =============================================================================

from typing import Optional, List
from pydantic_settings import BaseSettings
from pydantic import field_validator
import os
from functools import lru_cache


class Settings(BaseSettings):
    """Configurações da aplicação"""
    
    # =============================================================================
    # APLICAÇÃO
    # =============================================================================
    PROJECT_NAME: str = "Medium Analytics Bot"
    VERSION: str = "1.0.0"
    DESCRIPTION: str = "Sistema de analytics do Medium com automação LinkedIn"
    
    # Ambiente
    ENVIRONMENT: str = "development"
    DEBUG: bool = True
    SECRET_KEY: str = "your-secret-key-here"
    
    # API
    API_HOST: str = "0.0.0.0"
    API_PORT: int = 8000
    API_PREFIX: str = "/api/v1"
    
    # CORS
    CORS_ORIGINS: List[str] = [
        "http://localhost:3000",
        "http://localhost:8501",
        "http://localhost:8000"
    ]
    CORS_ALLOW_CREDENTIALS: bool = True
    CORS_ALLOW_METHODS: List[str] = ["*"]
    CORS_ALLOW_HEADERS: List[str] = ["*"]
    
    # =============================================================================
    # BANCO DE DADOS
    # =============================================================================
    DB_HOST: str = "localhost"
    DB_PORT: int = 5432
    DB_NAME: str = "medium_analytics"
    DB_USER: str = "postgres"
    DB_PASSWORD: str = "postgres123"
    DATABASE_URL: Optional[str] = None
    
    # SQLAlchemy
    SQLALCHEMY_ECHO: bool = False
    SQLALCHEMY_POOL_SIZE: int = 5
    SQLALCHEMY_MAX_OVERFLOW: int = 10
    
    @validator("DATABASE_URL", pre=True)
    def assemble_db_connection(cls, v: Optional[str], values: dict) -> str:
        if isinstance(v, str):
            return v
        return (
            f"postgresql://{values.get('DB_USER')}:"
            f"{values.get('DB_PASSWORD')}@"
            f"{values.get('DB_HOST')}:"
            f"{values.get('DB_PORT')}/"
            f"{values.get('DB_NAME')}"
        )
    
    # =============================================================================
    # REDIS
    # =============================================================================
    REDIS_HOST: str = "localhost"
    REDIS_PORT: int = 6379
    REDIS_DB: int = 0
    REDIS_URL: Optional[str] = None
    
    @validator("REDIS_URL", pre=True)
    def assemble_redis_connection(cls, v: Optional[str], values: dict) -> str:
        if isinstance(v, str):
            return v
        return (
            f"redis://{values.get('REDIS_HOST')}:"
            f"{values.get('REDIS_PORT')}/"
            f"{values.get('REDIS_DB')}"
        )
    
    # =============================================================================
    # CELERY
    # =============================================================================
    CELERY_BROKER_URL: Optional[str] = None
    CELERY_RESULT_BACKEND: Optional[str] = None
    CELERY_TASK_SERIALIZER: str = "json"
    CELERY_RESULT_SERIALIZER: str = "json"
    CELERY_ACCEPT_CONTENT: List[str] = ["json"]
    CELERY_TIMEZONE: str = "UTC"
    
    @validator("CELERY_BROKER_URL", pre=True)
    def assemble_celery_broker(cls, v: Optional[str], values: dict) -> str:
        if isinstance(v, str):
            return v
        return f"{values.get('REDIS_URL')}/0"
    
    @validator("CELERY_RESULT_BACKEND", pre=True)
    def assemble_celery_backend(cls, v: Optional[str], values: dict) -> str:
        if isinstance(v, str):
            return v
        return f"{values.get('REDIS_URL')}/0"
    
    # =============================================================================
    # AWS
    # =============================================================================
    AWS_REGION: str = "us-east-1"
    AWS_ACCESS_KEY_ID: Optional[str] = None
    AWS_SECRET_ACCESS_KEY: Optional[str] = None
    
    # S3
    S3_BUCKET_NAME: Optional[str] = None
    S3_REGION: str = "us-east-1"
    
    # SES
    EMAIL_BACKEND: str = "ses"
    AWS_SES_REGION: str = "us-east-1"
    FROM_EMAIL: Optional[str] = None
    ADMIN_EMAIL: Optional[str] = None
    
    # CloudWatch
    CLOUDWATCH_LOG_GROUP: str = "/aws/medium-analytics"
    CLOUDWATCH_LOG_STREAM: str = "application"
    
    # =============================================================================
    # APIS EXTERNAS
    # =============================================================================
    
    # Medium
    MEDIUM_RSS_URL: Optional[str] = None
    MEDIUM_USERNAME: Optional[str] = None
    
    # LinkedIn
    LINKEDIN_CLIENT_ID: Optional[str] = None
    LINKEDIN_CLIENT_SECRET: Optional[str] = None
    LINKEDIN_ACCESS_TOKEN: Optional[str] = None
    LINKEDIN_REDIRECT_URI: str = "http://localhost:8000/auth/linkedin/callback"
    
    # =============================================================================
    # AUTOMAÇÃO
    # =============================================================================
    
    # Frequências (em minutos/horas)
    RSS_CHECK_INTERVAL: int = 60  # minutos
    LINKEDIN_POST_DELAY: int = 2  # horas após publicação
    ANALYTICS_COLLECTION_INTERVAL: int = 6  # horas
    BACKUP_INTERVAL: int = 24  # horas
    
    # Thresholds
    HIGH_PERFORMANCE_VIEWS_THRESHOLD: int = 1000
    HIGH_PERFORMANCE_READ_RATIO_THRESHOLD: float = 0.5
    
    # =============================================================================
    # NOTIFICAÇÕES
    # =============================================================================
    ENABLE_EMAIL_ALERTS: bool = True
    WEEKLY_REPORT_DAY: str = "sunday"
    WEEKLY_REPORT_TIME: str = "09:00"
    
    # =============================================================================
    # RATE LIMITING
    # =============================================================================
    RATE_LIMIT_PER_MINUTE: int = 60
    RATE_LIMIT_BURST: int = 10
    
    # =============================================================================
    # LOGGING
    # =============================================================================
    LOG_LEVEL: str = "INFO"
    LOG_FORMAT: str = "{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} | {message}"
    
    # =============================================================================
    # TESTING
    # =============================================================================
    TESTING: bool = False
    TEST_DATABASE_URL: Optional[str] = None
    
    class Config:
        env_file = ".env"
        case_sensitive = True


@lru_cache()
def get_settings() -> Settings:
    """Obter configurações (cached)"""
    return Settings()


# Instância global das configurações
settings = get_settings()


# =============================================================================
# VALIDAÇÕES
# =============================================================================

def validate_required_settings():
    """Validar configurações obrigatórias"""
    required_for_production = [
        "SECRET_KEY",
        "DATABASE_URL",
        "REDIS_URL"
    ]
    
    if settings.ENVIRONMENT == "production":
        required_for_production.extend([
            "AWS_ACCESS_KEY_ID",
            "AWS_SECRET_ACCESS_KEY",
            "S3_BUCKET_NAME",
            "MEDIUM_RSS_URL"
        ])
    
    missing = []
    for setting in required_for_production:
        if not getattr(settings, setting):
            missing.append(setting)
    
    if missing:
        raise ValueError(f"Configurações obrigatórias não definidas: {', '.join(missing)}")


def get_database_url() -> str:
    """Obter URL do banco de dados"""
    return settings.DATABASE_URL


def get_redis_url() -> str:
    """Obter URL do Redis"""
    return settings.REDIS_URL


def is_development() -> bool:
    """Verificar se está em desenvolvimento"""
    return settings.ENVIRONMENT == "development"


def is_production() -> bool:
    """Verificar se está em produção"""
    return settings.ENVIRONMENT == "production"


def is_testing() -> bool:
    """Verificar se está em modo de teste"""
    return settings.TESTING
