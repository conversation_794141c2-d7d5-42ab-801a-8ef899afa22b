# =============================================================================
# MEDIUM ANALYTICS BOT - CONFIGURAÇÃO DO BANCO DE DADOS
# =============================================================================

from sqlalchemy import create_engine, MetaData
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.pool import StaticPool
from typing import Generator
import logging

from .config import settings

# =============================================================================
# CONFIGURAÇÃO DO SQLALCHEMY
# =============================================================================

# Engine do banco
if settings.TESTING:
    # Para testes, usar SQLite em memória
    engine = create_engine(
        "sqlite:///:memory:",
        poolclass=StaticPool,
        connect_args={"check_same_thread": False},
        echo=settings.SQLALCHEMY_ECHO
    )
else:
    # Para desenvolvimento/produção, usar PostgreSQL
    engine = create_engine(
        settings.DATABASE_URL,
        pool_size=settings.SQLALCHEMY_POOL_SIZE,
        max_overflow=settings.SQLALCHEMY_MAX_OVERFLOW,
        echo=settings.SQLALCHEMY_ECHO,
        pool_pre_ping=True,  # Verificar conexões antes de usar
        pool_recycle=3600,   # Reciclar conexões a cada hora
    )

# Session factory
SessionLocal = sessionmaker(
    autocommit=False,
    autoflush=False,
    bind=engine
)

# Base para modelos
Base = declarative_base()

# Metadata para migrações
metadata = MetaData()

# =============================================================================
# DEPENDÊNCIAS
# =============================================================================

def get_db() -> Generator[Session, None, None]:
    """
    Dependency para obter sessão do banco de dados
    
    Yields:
        Session: Sessão do SQLAlchemy
    """
    db = SessionLocal()
    try:
        yield db
    except Exception as e:
        logging.error(f"Erro na sessão do banco: {e}")
        db.rollback()
        raise
    finally:
        db.close()


# =============================================================================
# FUNÇÕES UTILITÁRIAS
# =============================================================================

def create_tables():
    """Criar todas as tabelas"""
    Base.metadata.create_all(bind=engine)


def drop_tables():
    """Dropar todas as tabelas (CUIDADO!)"""
    Base.metadata.drop_all(bind=engine)


def check_connection() -> bool:
    """
    Verificar se a conexão com o banco está funcionando
    
    Returns:
        bool: True se conectou com sucesso
    """
    try:
        with engine.connect() as conn:
            conn.execute("SELECT 1")
        return True
    except Exception as e:
        logging.error(f"Erro ao conectar com o banco: {e}")
        return False


def get_db_info() -> dict:
    """
    Obter informações sobre o banco de dados
    
    Returns:
        dict: Informações do banco
    """
    try:
        with engine.connect() as conn:
            # PostgreSQL
            if "postgresql" in settings.DATABASE_URL:
                result = conn.execute("SELECT version()")
                version = result.fetchone()[0]
                
                result = conn.execute("SELECT current_database()")
                database = result.fetchone()[0]
                
                return {
                    "type": "PostgreSQL",
                    "version": version,
                    "database": database,
                    "url": settings.DATABASE_URL.split("@")[1] if "@" in settings.DATABASE_URL else "hidden"
                }
            
            # SQLite (para testes)
            elif "sqlite" in settings.DATABASE_URL:
                result = conn.execute("SELECT sqlite_version()")
                version = result.fetchone()[0]
                
                return {
                    "type": "SQLite",
                    "version": version,
                    "database": ":memory:" if ":memory:" in settings.DATABASE_URL else "file",
                    "url": "in-memory" if ":memory:" in settings.DATABASE_URL else settings.DATABASE_URL
                }
            
            else:
                return {
                    "type": "Unknown",
                    "version": "Unknown",
                    "database": "Unknown",
                    "url": "Unknown"
                }
                
    except Exception as e:
        logging.error(f"Erro ao obter informações do banco: {e}")
        return {
            "type": "Error",
            "version": str(e),
            "database": "Error",
            "url": "Error"
        }


# =============================================================================
# CONTEXT MANAGERS
# =============================================================================

class DatabaseSession:
    """Context manager para sessões do banco"""
    
    def __init__(self):
        self.db = None
    
    def __enter__(self) -> Session:
        self.db = SessionLocal()
        return self.db
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        if exc_type:
            self.db.rollback()
        else:
            self.db.commit()
        self.db.close()


# =============================================================================
# FUNÇÕES DE INICIALIZAÇÃO
# =============================================================================

def init_db():
    """Inicializar banco de dados"""
    try:
        # Verificar conexão
        if not check_connection():
            raise Exception("Não foi possível conectar ao banco de dados")
        
        # Criar tabelas se não existirem
        create_tables()
        
        logging.info("Banco de dados inicializado com sucesso")
        
    except Exception as e:
        logging.error(f"Erro ao inicializar banco de dados: {e}")
        raise


def reset_db():
    """Resetar banco de dados (CUIDADO!)"""
    if not settings.TESTING:
        raise Exception("Reset do banco só é permitido em modo de teste")
    
    try:
        drop_tables()
        create_tables()
        logging.info("Banco de dados resetado com sucesso")
        
    except Exception as e:
        logging.error(f"Erro ao resetar banco de dados: {e}")
        raise


# =============================================================================
# HEALTH CHECK
# =============================================================================

async def health_check() -> dict:
    """
    Health check do banco de dados
    
    Returns:
        dict: Status do banco
    """
    try:
        # Verificar conexão
        connected = check_connection()
        
        if connected:
            info = get_db_info()
            return {
                "status": "healthy",
                "connected": True,
                "info": info
            }
        else:
            return {
                "status": "unhealthy",
                "connected": False,
                "error": "Não foi possível conectar ao banco"
            }
            
    except Exception as e:
        return {
            "status": "error",
            "connected": False,
            "error": str(e)
        }
