# =============================================================================
# MEDIUM ANALYTICS BOT - CONFIGURAÇÃO DO REDIS/CACHE
# =============================================================================

import redis
import json
import pickle
from typing import Any, Optional, Union
from datetime import timedelta
import logging

from .config import settings

# =============================================================================
# CONFIGURAÇÃO DO REDIS
# =============================================================================

# Cliente Redis
redis_client = redis.from_url(
    settings.REDIS_URL,
    decode_responses=True,
    socket_connect_timeout=5,
    socket_timeout=5,
    retry_on_timeout=True,
    health_check_interval=30
)

# Cliente Redis para dados binários (pickle)
redis_binary_client = redis.from_url(
    settings.REDIS_URL,
    decode_responses=False,  # Para dados binários
    socket_connect_timeout=5,
    socket_timeout=5,
    retry_on_timeout=True
)

# =============================================================================
# CACHE MANAGER
# =============================================================================

class CacheManager:
    """Gerenciador de cache Redis"""
    
    def __init__(self, client: redis.Redis = redis_client):
        self.client = client
        self.binary_client = redis_binary_client
    
    def get(self, key: str) -> Optional[Any]:
        """
        Obter valor do cache
        
        Args:
            key: Chave do cache
            
        Returns:
            Valor do cache ou None se não existir
        """
        try:
            value = self.client.get(key)
            if value is None:
                return None
            
            # Tentar deserializar JSON
            try:
                return json.loads(value)
            except (json.JSONDecodeError, TypeError):
                return value
                
        except Exception as e:
            logging.error(f"Erro ao obter cache {key}: {e}")
            return None
    
    def set(
        self, 
        key: str, 
        value: Any, 
        ttl: Optional[Union[int, timedelta]] = None
    ) -> bool:
        """
        Definir valor no cache
        
        Args:
            key: Chave do cache
            value: Valor a ser armazenado
            ttl: Tempo de vida (segundos ou timedelta)
            
        Returns:
            True se sucesso, False caso contrário
        """
        try:
            # Serializar valor
            if isinstance(value, (dict, list, tuple)):
                serialized_value = json.dumps(value, default=str)
            else:
                serialized_value = str(value)
            
            # Definir TTL
            if isinstance(ttl, timedelta):
                ttl = int(ttl.total_seconds())
            
            # Armazenar no Redis
            return self.client.set(key, serialized_value, ex=ttl)
            
        except Exception as e:
            logging.error(f"Erro ao definir cache {key}: {e}")
            return False
    
    def delete(self, key: str) -> bool:
        """
        Deletar chave do cache
        
        Args:
            key: Chave a ser deletada
            
        Returns:
            True se deletou, False caso contrário
        """
        try:
            return bool(self.client.delete(key))
        except Exception as e:
            logging.error(f"Erro ao deletar cache {key}: {e}")
            return False
    
    def exists(self, key: str) -> bool:
        """
        Verificar se chave existe
        
        Args:
            key: Chave a verificar
            
        Returns:
            True se existe, False caso contrário
        """
        try:
            return bool(self.client.exists(key))
        except Exception as e:
            logging.error(f"Erro ao verificar cache {key}: {e}")
            return False
    
    def get_ttl(self, key: str) -> Optional[int]:
        """
        Obter TTL de uma chave
        
        Args:
            key: Chave a verificar
            
        Returns:
            TTL em segundos ou None se não existe
        """
        try:
            ttl = self.client.ttl(key)
            return ttl if ttl > 0 else None
        except Exception as e:
            logging.error(f"Erro ao obter TTL {key}: {e}")
            return None
    
    def increment(self, key: str, amount: int = 1) -> Optional[int]:
        """
        Incrementar valor numérico
        
        Args:
            key: Chave a incrementar
            amount: Quantidade a incrementar
            
        Returns:
            Novo valor ou None se erro
        """
        try:
            return self.client.incrby(key, amount)
        except Exception as e:
            logging.error(f"Erro ao incrementar {key}: {e}")
            return None
    
    def get_binary(self, key: str) -> Optional[Any]:
        """
        Obter objeto serializado com pickle
        
        Args:
            key: Chave do cache
            
        Returns:
            Objeto deserializado ou None
        """
        try:
            value = self.binary_client.get(key)
            if value is None:
                return None
            return pickle.loads(value)
        except Exception as e:
            logging.error(f"Erro ao obter cache binário {key}: {e}")
            return None
    
    def set_binary(
        self, 
        key: str, 
        value: Any, 
        ttl: Optional[Union[int, timedelta]] = None
    ) -> bool:
        """
        Armazenar objeto com pickle
        
        Args:
            key: Chave do cache
            value: Objeto a ser armazenado
            ttl: Tempo de vida
            
        Returns:
            True se sucesso
        """
        try:
            serialized = pickle.dumps(value)
            
            if isinstance(ttl, timedelta):
                ttl = int(ttl.total_seconds())
            
            return self.binary_client.set(key, serialized, ex=ttl)
        except Exception as e:
            logging.error(f"Erro ao definir cache binário {key}: {e}")
            return False
    
    def clear_pattern(self, pattern: str) -> int:
        """
        Limpar chaves que correspondem a um padrão
        
        Args:
            pattern: Padrão das chaves (ex: "user:*")
            
        Returns:
            Número de chaves deletadas
        """
        try:
            keys = self.client.keys(pattern)
            if keys:
                return self.client.delete(*keys)
            return 0
        except Exception as e:
            logging.error(f"Erro ao limpar padrão {pattern}: {e}")
            return 0


# =============================================================================
# INSTÂNCIA GLOBAL
# =============================================================================

cache = CacheManager()

# =============================================================================
# FUNÇÕES DE CONVENIÊNCIA
# =============================================================================

def get_cache(key: str) -> Optional[Any]:
    """Obter valor do cache"""
    return cache.get(key)


def set_cache(
    key: str, 
    value: Any, 
    ttl: Optional[Union[int, timedelta]] = None
) -> bool:
    """Definir valor no cache"""
    return cache.set(key, value, ttl)


def delete_cache(key: str) -> bool:
    """Deletar chave do cache"""
    return cache.delete(key)


def cache_exists(key: str) -> bool:
    """Verificar se chave existe no cache"""
    return cache.exists(key)


# =============================================================================
# DECORADOR DE CACHE
# =============================================================================

def cached(ttl: Union[int, timedelta] = 300, key_prefix: str = ""):
    """
    Decorador para cache de funções
    
    Args:
        ttl: Tempo de vida do cache
        key_prefix: Prefixo para a chave
    """
    def decorator(func):
        def wrapper(*args, **kwargs):
            # Gerar chave do cache
            key_parts = [key_prefix, func.__name__]
            if args:
                key_parts.extend([str(arg) for arg in args])
            if kwargs:
                key_parts.extend([f"{k}:{v}" for k, v in sorted(kwargs.items())])
            
            cache_key = ":".join(filter(None, key_parts))
            
            # Tentar obter do cache
            cached_result = cache.get(cache_key)
            if cached_result is not None:
                return cached_result
            
            # Executar função e cachear resultado
            result = func(*args, **kwargs)
            cache.set(cache_key, result, ttl)
            
            return result
        
        return wrapper
    return decorator


# =============================================================================
# HEALTH CHECK
# =============================================================================

def check_redis_connection() -> bool:
    """
    Verificar conexão com Redis
    
    Returns:
        True se conectado
    """
    try:
        redis_client.ping()
        return True
    except Exception as e:
        logging.error(f"Erro ao conectar com Redis: {e}")
        return False


async def redis_health_check() -> dict:
    """
    Health check do Redis
    
    Returns:
        dict: Status do Redis
    """
    try:
        # Verificar conexão
        connected = check_redis_connection()
        
        if connected:
            info = redis_client.info()
            return {
                "status": "healthy",
                "connected": True,
                "info": {
                    "version": info.get("redis_version"),
                    "used_memory": info.get("used_memory_human"),
                    "connected_clients": info.get("connected_clients"),
                    "uptime": info.get("uptime_in_seconds")
                }
            }
        else:
            return {
                "status": "unhealthy",
                "connected": False,
                "error": "Não foi possível conectar ao Redis"
            }
            
    except Exception as e:
        return {
            "status": "error",
            "connected": False,
            "error": str(e)
        }
