# =============================================================================
# MEDIUM ANALYTICS BOT - API DE ANALYTICS
# =============================================================================

from fastapi import APIRouter, Depends, Query
from sqlalchemy.orm import Session
from typing import List, Optional
from datetime import datetime, timedelta, date

from ...core.database import get_db
from ...models.analytics import AnalyticsSummary
from ...models.article import Article, ArticleMetrics
from ...models.linkedin_post import LinkedInPost, LinkedInMetrics

router = APIRouter()


@router.get("/summary")
async def get_analytics_summary(
    start_date: Optional[date] = Query(None, description="Data inicial (YYYY-MM-DD)"),
    end_date: Optional[date] = Query(None, description="Data final (YYYY-MM-DD)"),
    days: Optional[int] = Query(30, ge=1, le=365, description="Últimos N dias (se datas não especificadas)"),
    db: Session = Depends(get_db)
):
    """
    Obter resumo analítico para um período
    """
    # Definir período
    if not start_date or not end_date:
        end_date = date.today()
        start_date = end_date - timedelta(days=days)
    
    # Obter summary agregado
    summary = AnalyticsSummary.get_period_summary(db, start_date, end_date)
    
    # Adicionar informações do período
    summary.update({
        "start_date": start_date.isoformat(),
        "end_date": end_date.isoformat(),
        "period_days": (end_date - start_date).days + 1
    })
    
    return summary


@router.get("/daily")
async def get_daily_analytics(
    start_date: Optional[date] = Query(None, description="Data inicial (YYYY-MM-DD)"),
    end_date: Optional[date] = Query(None, description="Data final (YYYY-MM-DD)"),
    days: Optional[int] = Query(30, ge=1, le=365, description="Últimos N dias"),
    db: Session = Depends(get_db)
):
    """
    Obter analytics diários
    """
    # Definir período
    if not start_date or not end_date:
        end_date = date.today()
        start_date = end_date - timedelta(days=days)
    
    # Buscar summaries diários
    summaries = db.query(AnalyticsSummary).filter(
        AnalyticsSummary.date >= start_date,
        AnalyticsSummary.date <= end_date
    ).order_by(AnalyticsSummary.date).all()
    
    return {
        "start_date": start_date.isoformat(),
        "end_date": end_date.isoformat(),
        "daily_data": [summary.to_dict() for summary in summaries]
    }


@router.get("/trends")
async def get_analytics_trends(
    metric: str = Query("views", description="Métrica para análise de tendência"),
    days: int = Query(30, ge=7, le=365, description="Período em dias"),
    db: Session = Depends(get_db)
):
    """
    Obter tendências de métricas
    """
    end_date = date.today()
    start_date = end_date - timedelta(days=days)
    
    # Buscar dados diários
    summaries = db.query(AnalyticsSummary).filter(
        AnalyticsSummary.date >= start_date,
        AnalyticsSummary.date <= end_date
    ).order_by(AnalyticsSummary.date).all()
    
    if not summaries:
        return {
            "metric": metric,
            "period_days": days,
            "trend": "no_data",
            "change_percentage": 0,
            "data_points": []
        }
    
    # Extrair valores da métrica
    metric_mapping = {
        "views": "total_views",
        "reads": "total_reads", 
        "claps": "total_claps",
        "articles": "articles_published",
        "linkedin_impressions": "linkedin_impressions",
        "linkedin_clicks": "linkedin_clicks"
    }
    
    if metric not in metric_mapping:
        return {"error": f"Métrica '{metric}' não suportada"}
    
    attr_name = metric_mapping[metric]
    values = [getattr(summary, attr_name, 0) for summary in summaries]
    
    # Calcular tendência
    if len(values) < 2:
        trend = "insufficient_data"
        change_percentage = 0
    else:
        # Comparar primeira e segunda metade do período
        mid_point = len(values) // 2
        first_half_avg = sum(values[:mid_point]) / mid_point if mid_point > 0 else 0
        second_half_avg = sum(values[mid_point:]) / (len(values) - mid_point)
        
        if first_half_avg == 0:
            change_percentage = 100 if second_half_avg > 0 else 0
        else:
            change_percentage = ((second_half_avg - first_half_avg) / first_half_avg) * 100
        
        if change_percentage > 10:
            trend = "increasing"
        elif change_percentage < -10:
            trend = "decreasing"
        else:
            trend = "stable"
    
    return {
        "metric": metric,
        "period_days": days,
        "trend": trend,
        "change_percentage": round(change_percentage, 2),
        "first_half_avg": round(first_half_avg, 2) if 'first_half_avg' in locals() else 0,
        "second_half_avg": round(second_half_avg, 2) if 'second_half_avg' in locals() else 0,
        "data_points": [
            {
                "date": summary.date.isoformat(),
                "value": getattr(summary, attr_name, 0)
            }
            for summary in summaries
        ]
    }


@router.get("/performance")
async def get_performance_metrics(
    days: int = Query(30, ge=1, le=365, description="Período em dias"),
    db: Session = Depends(get_db)
):
    """
    Obter métricas de performance
    """
    cutoff_date = datetime.utcnow() - timedelta(days=days)
    
    # Artigos no período
    articles = db.query(Article).filter(
        Article.published_at >= cutoff_date
    ).all()
    
    if not articles:
        return {
            "period_days": days,
            "total_articles": 0,
            "performance_metrics": {}
        }
    
    # Calcular métricas de performance
    total_views = sum(article.total_views for article in articles)
    total_reads = sum(article.total_reads for article in articles)
    total_claps = sum(article.total_claps for article in articles)
    
    # Métricas por artigo
    avg_views_per_article = total_views / len(articles)
    avg_reads_per_article = total_reads / len(articles)
    avg_claps_per_article = total_claps / len(articles)
    
    # Taxa de leitura geral
    overall_read_ratio = total_reads / total_views if total_views > 0 else 0
    
    # Distribuição de performance
    high_performance = [a for a in articles if a.is_high_performance]
    medium_performance = [a for a in articles if not a.is_high_performance and a.total_views >= avg_views_per_article]
    low_performance = [a for a in articles if a.total_views < avg_views_per_article and not a.is_high_performance]
    
    # Top e bottom performers
    top_performers = sorted(articles, key=lambda a: a.total_views, reverse=True)[:5]
    bottom_performers = sorted(articles, key=lambda a: a.total_views)[:5]
    
    return {
        "period_days": days,
        "total_articles": len(articles),
        "performance_metrics": {
            "total_views": total_views,
            "total_reads": total_reads,
            "total_claps": total_claps,
            "avg_views_per_article": round(avg_views_per_article, 2),
            "avg_reads_per_article": round(avg_reads_per_article, 2),
            "avg_claps_per_article": round(avg_claps_per_article, 2),
            "overall_read_ratio": round(overall_read_ratio, 4)
        },
        "performance_distribution": {
            "high_performance": {
                "count": len(high_performance),
                "percentage": round(len(high_performance) / len(articles) * 100, 2)
            },
            "medium_performance": {
                "count": len(medium_performance),
                "percentage": round(len(medium_performance) / len(articles) * 100, 2)
            },
            "low_performance": {
                "count": len(low_performance),
                "percentage": round(len(low_performance) / len(articles) * 100, 2)
            }
        },
        "top_performers": [
            {
                "id": article.id,
                "title": article.title[:100] + "..." if len(article.title) > 100 else article.title,
                "views": article.total_views,
                "reads": article.total_reads,
                "claps": article.total_claps,
                "read_ratio": article.read_ratio,
                "engagement_score": round(article.engagement_score, 4)
            }
            for article in top_performers
        ],
        "improvement_opportunities": [
            {
                "id": article.id,
                "title": article.title[:100] + "..." if len(article.title) > 100 else article.title,
                "views": article.total_views,
                "read_ratio": article.read_ratio,
                "potential": "low_views" if article.total_views < avg_views_per_article else "low_engagement"
            }
            for article in bottom_performers
        ]
    }


@router.post("/update-daily/{target_date}")
async def update_daily_summary(
    target_date: date,
    db: Session = Depends(get_db)
):
    """
    Atualizar summary diário para uma data específica
    """
    try:
        summary = AnalyticsSummary.update_daily_summary(db, target_date)
        return {
            "message": f"Summary atualizado para {target_date}",
            "summary": summary.to_dict()
        }
    except Exception as e:
        return {
            "error": f"Erro ao atualizar summary: {str(e)}"
        }
