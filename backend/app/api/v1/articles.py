# =============================================================================
# MEDIUM ANALYTICS BOT - API DE ARTIGOS
# =============================================================================

from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from typing import List, Optional
from datetime import datetime, timedelta

from ...core.database import get_db
from ...models.article import Article, ArticleMetrics
from ...services.medium_service import MediumService

router = APIRouter()


@router.get("/", response_model=List[dict])
async def get_articles(
    skip: int = Query(0, ge=0, description="Número de registros para pular"),
    limit: int = Query(100, ge=1, le=1000, description="Número máximo de registros"),
    search: Optional[str] = Query(None, description="Buscar por título"),
    tags: Optional[str] = Query(None, description="Filtrar por tags (separadas por vírgula)"),
    days: Optional[int] = Query(None, ge=1, description="Filtrar por dias desde publicação"),
    db: Session = Depends(get_db)
):
    """
    Listar artigos com filtros opcionais
    """
    query = db.query(Article)
    
    # Filtro por busca no título
    if search:
        query = query.filter(Article.title.ilike(f"%{search}%"))
    
    # Filtro por tags
    if tags:
        tag_list = [tag.strip() for tag in tags.split(",")]
        for tag in tag_list:
            query = query.filter(Article.tags.any(tag))
    
    # Filtro por dias
    if days:
        cutoff_date = datetime.utcnow() - timedelta(days=days)
        query = query.filter(Article.published_at >= cutoff_date)
    
    # Ordenar por data de publicação (mais recente primeiro)
    query = query.order_by(Article.published_at.desc())
    
    # Paginação
    articles = query.offset(skip).limit(limit).all()
    
    return [article.to_dict() for article in articles]


@router.get("/{article_id}", response_model=dict)
async def get_article(
    article_id: int,
    db: Session = Depends(get_db)
):
    """
    Obter artigo específico por ID
    """
    article = db.query(Article).filter(Article.id == article_id).first()
    
    if not article:
        raise HTTPException(status_code=404, detail="Artigo não encontrado")
    
    return article.to_dict()


@router.get("/{article_id}/metrics", response_model=List[dict])
async def get_article_metrics(
    article_id: int,
    days: int = Query(30, ge=1, le=365, description="Número de dias de histórico"),
    db: Session = Depends(get_db)
):
    """
    Obter histórico de métricas de um artigo
    """
    article = db.query(Article).filter(Article.id == article_id).first()
    
    if not article:
        raise HTTPException(status_code=404, detail="Artigo não encontrado")
    
    # Obter métricas dos últimos N dias
    cutoff_date = datetime.utcnow() - timedelta(days=days)
    metrics = db.query(ArticleMetrics).filter(
        ArticleMetrics.article_id == article_id,
        ArticleMetrics.collected_at >= cutoff_date
    ).order_by(ArticleMetrics.collected_at.desc()).all()
    
    return [metric.to_dict() for metric in metrics]


@router.post("/sync")
async def sync_articles(
    db: Session = Depends(get_db)
):
    """
    Sincronizar artigos do Medium RSS
    """
    try:
        medium_service = MediumService(db)
        new_articles = await medium_service.sync_articles()
        
        return {
            "message": "Sincronização concluída",
            "new_articles": len(new_articles),
            "articles": [article.to_dict() for article in new_articles]
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Erro na sincronização: {str(e)}")


@router.get("/stats/summary")
async def get_articles_summary(
    days: int = Query(30, ge=1, le=365, description="Período em dias"),
    db: Session = Depends(get_db)
):
    """
    Obter resumo estatístico dos artigos
    """
    cutoff_date = datetime.utcnow() - timedelta(days=days)
    
    # Artigos no período
    articles = db.query(Article).filter(
        Article.published_at >= cutoff_date
    ).all()
    
    if not articles:
        return {
            "period_days": days,
            "total_articles": 0,
            "total_views": 0,
            "total_reads": 0,
            "total_claps": 0,
            "avg_read_ratio": 0.0,
            "high_performance_articles": 0
        }
    
    # Calcular estatísticas
    total_views = sum(article.total_views for article in articles)
    total_reads = sum(article.total_reads for article in articles)
    total_claps = sum(article.total_claps for article in articles)
    
    # Taxa de leitura média
    avg_read_ratio = total_reads / total_views if total_views > 0 else 0.0
    
    # Artigos de alta performance
    high_performance = sum(1 for article in articles if article.is_high_performance)
    
    # Top performers
    top_articles = sorted(articles, key=lambda a: a.total_views, reverse=True)[:5]
    
    return {
        "period_days": days,
        "total_articles": len(articles),
        "total_views": total_views,
        "total_reads": total_reads,
        "total_claps": total_claps,
        "avg_read_ratio": round(avg_read_ratio, 4),
        "high_performance_articles": high_performance,
        "top_performers": [
            {
                "id": article.id,
                "title": article.title,
                "views": article.total_views,
                "read_ratio": article.read_ratio
            }
            for article in top_articles
        ]
    }


@router.get("/tags/popular")
async def get_popular_tags(
    limit: int = Query(20, ge=1, le=100, description="Número máximo de tags"),
    days: int = Query(30, ge=1, le=365, description="Período em dias"),
    db: Session = Depends(get_db)
):
    """
    Obter tags mais populares
    """
    cutoff_date = datetime.utcnow() - timedelta(days=days)

    # Buscar artigos no período
    articles = db.query(Article).filter(
        Article.published_at >= cutoff_date,
        Article.tags.isnot(None)
    ).all()

    # Contar tags
    tag_counts = {}
    for article in articles:
        if article.tags:
            for tag in article.tags:
                tag_counts[tag] = tag_counts.get(tag, 0) + 1

    # Ordenar por popularidade
    popular_tags = sorted(tag_counts.items(), key=lambda x: x[1], reverse=True)[:limit]

    return [
        {
            "tag": tag,
            "count": count,
            "percentage": round(count / len(articles) * 100, 2) if articles else 0
        }
        for tag, count in popular_tags
    ]


@router.post("/{article_id}/simulate-metrics")
async def simulate_article_metrics(
    article_id: int,
    db: Session = Depends(get_db)
):
    """
    Simular métricas para um artigo (para desenvolvimento/teste)
    """
    try:
        medium_service = MediumService(db)
        metrics = await medium_service.simulate_metrics_update(article_id)

        if not metrics:
            raise HTTPException(status_code=404, detail="Artigo não encontrado")

        return {
            "message": "Métricas simuladas com sucesso",
            "metrics": metrics.to_dict()
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Erro ao simular métricas: {str(e)}")


@router.post("/simulate-all-metrics")
async def simulate_all_metrics(
    db: Session = Depends(get_db)
):
    """
    Simular métricas para todos os artigos
    """
    try:
        articles = db.query(Article).all()
        medium_service = MediumService(db)

        updated_count = 0
        for article in articles:
            try:
                await medium_service.simulate_metrics_update(article.id)
                updated_count += 1
            except Exception as e:
                print(f"Erro ao simular métricas para artigo {article.id}: {e}")
                continue

        return {
            "message": f"Métricas simuladas para {updated_count} artigos",
            "total_articles": len(articles),
            "updated_articles": updated_count
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Erro ao simular métricas: {str(e)}")
