# =============================================================================
# MEDIUM ANALYTICS BOT - BACKEND DEPENDENCIES
# =============================================================================

# Web Framework
fastapi==0.104.1
uvicorn[standard]==0.24.0
pydantic==2.5.0
pydantic-settings==2.1.0

# Database
sqlalchemy==2.0.23
alembic==1.13.1
psycopg2-binary==2.9.9

# Redis & Caching
redis==5.0.1
redis[hiredis]==5.0.1

# Task Queue
celery==5.3.4

# AWS SDK
boto3==1.34.0
botocore==1.34.0

# HTTP Clients
httpx==0.25.2
requests==2.31.0

# Data Processing
pandas==2.1.4
numpy==1.25.2

# RSS & Web Scraping
feedparser==6.0.10
beautifulsoup4==4.12.2
lxml==4.9.3

# Authentication & Security
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
python-multipart==0.0.6

# Email & Validation
email-validator==2.1.0

# Configuration
python-dotenv==1.0.0

# Logging
loguru==0.7.2

# Date & Time
python-dateutil==2.8.2

# Utilities
click==8.1.7
rich==13.7.0

# Testing
pytest==7.4.3
pytest-asyncio==0.21.1
pytest-cov==4.1.0
httpx==0.25.2

# Development
black==23.11.0
isort==5.12.0
flake8==6.1.0
mypy==1.7.1

# Monitoring
prometheus-client==0.19.0

# LinkedIn API
linkedin-api==2.0.0

# Scheduling
apscheduler==3.10.4

# JSON & Data Serialization
orjson==3.9.10

# CORS
fastapi-cors==0.0.6

# Rate Limiting
slowapi==0.1.9

# File Processing
openpyxl==3.1.2
xlsxwriter==3.1.9

# Image Processing (for social media)
pillow==10.1.0

# Template Engine
jinja2==3.1.2

# Timezone
pytz==2023.3

# UUID
uuid==1.30

# Validation
validators==0.22.0

# Async Support
asyncio==3.4.3
aiofiles==23.2.1
aioredis==2.0.1

# Development Server
watchdog==3.0.0
