"""Initial migration - create all tables

Revision ID: 001
Revises: 
Create Date: 2024-12-21 10:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '001'
down_revision = None
branch_labels = None
depends_on = None


def upgrade() -> None:
    # Create articles table
    op.create_table('articles',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('guid', sa.String(length=255), nullable=False),
        sa.Column('title', sa.String(length=500), nullable=False),
        sa.Column('url', sa.String(length=500), nullable=False),
        sa.Column('content', sa.Text(), nullable=True),
        sa.Column('summary', sa.Text(), nullable=True),
        sa.Column('published_at', sa.DateTime(timezone=True), nullable=False),
        sa.Column('reading_time', sa.Integer(), nullable=True),
        sa.Column('word_count', sa.Integer(), nullable=True),
        sa.Column('tags', sa.ARRAY(sa.String()), nullable=True),
        sa.Column('categories', sa.ARRAY(sa.String()), nullable=True),
        sa.Column('is_active', sa.Boolean(), nullable=True),
        sa.Column('is_featured', sa.Boolean(), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_articles_guid'), 'articles', ['guid'], unique=True)
    op.create_index(op.f('ix_articles_id'), 'articles', ['id'], unique=False)
    op.create_index(op.f('ix_articles_published_at'), 'articles', ['published_at'], unique=False)

    # Create article_metrics table
    op.create_table('article_metrics',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('article_id', sa.Integer(), nullable=False),
        sa.Column('views', sa.Integer(), nullable=True),
        sa.Column('reads', sa.Integer(), nullable=True),
        sa.Column('claps', sa.Integer(), nullable=True),
        sa.Column('responses', sa.Integer(), nullable=True),
        sa.Column('fans', sa.Integer(), nullable=True),
        sa.Column('read_ratio', sa.Numeric(precision=5, scale=4), nullable=True),
        sa.Column('collected_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.ForeignKeyConstraint(['article_id'], ['articles.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_article_metrics_article_id'), 'article_metrics', ['article_id'], unique=False)
    op.create_index(op.f('ix_article_metrics_collected_at'), 'article_metrics', ['collected_at'], unique=False)
    op.create_index(op.f('ix_article_metrics_id'), 'article_metrics', ['id'], unique=False)

    # Create linkedin_posts table
    op.create_table('linkedin_posts',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('article_id', sa.Integer(), nullable=False),
        sa.Column('linkedin_post_id', sa.String(length=255), nullable=True),
        sa.Column('linkedin_urn', sa.String(length=500), nullable=True),
        sa.Column('content', sa.Text(), nullable=False),
        sa.Column('template_used', sa.String(length=100), nullable=True),
        sa.Column('hashtags', sa.Text(), nullable=True),
        sa.Column('scheduled_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('published_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('status', sa.String(length=50), nullable=True),
        sa.Column('error_message', sa.Text(), nullable=True),
        sa.Column('retry_count', sa.Integer(), nullable=True),
        sa.Column('auto_generated', sa.Boolean(), nullable=True),
        sa.Column('is_promoted', sa.Boolean(), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.ForeignKeyConstraint(['article_id'], ['articles.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_linkedin_posts_article_id'), 'linkedin_posts', ['article_id'], unique=False)
    op.create_index(op.f('ix_linkedin_posts_id'), 'linkedin_posts', ['id'], unique=False)
    op.create_index(op.f('ix_linkedin_posts_linkedin_post_id'), 'linkedin_posts', ['linkedin_post_id'], unique=True)
    op.create_index(op.f('ix_linkedin_posts_published_at'), 'linkedin_posts', ['published_at'], unique=False)
    op.create_index(op.f('ix_linkedin_posts_scheduled_at'), 'linkedin_posts', ['scheduled_at'], unique=False)
    op.create_index(op.f('ix_linkedin_posts_status'), 'linkedin_posts', ['status'], unique=False)

    # Create linkedin_metrics table
    op.create_table('linkedin_metrics',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('linkedin_post_id', sa.Integer(), nullable=False),
        sa.Column('impressions', sa.Integer(), nullable=True),
        sa.Column('clicks', sa.Integer(), nullable=True),
        sa.Column('likes', sa.Integer(), nullable=True),
        sa.Column('comments', sa.Integer(), nullable=True),
        sa.Column('shares', sa.Integer(), nullable=True),
        sa.Column('collected_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.ForeignKeyConstraint(['linkedin_post_id'], ['linkedin_posts.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_linkedin_metrics_collected_at'), 'linkedin_metrics', ['collected_at'], unique=False)
    op.create_index(op.f('ix_linkedin_metrics_id'), 'linkedin_metrics', ['id'], unique=False)
    op.create_index(op.f('ix_linkedin_metrics_linkedin_post_id'), 'linkedin_metrics', ['linkedin_post_id'], unique=False)

    # Create user_settings table
    op.create_table('user_settings',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('key', sa.String(length=100), nullable=False),
        sa.Column('value', sa.JSON(), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_user_settings_id'), 'user_settings', ['id'], unique=False)
    op.create_index(op.f('ix_user_settings_key'), 'user_settings', ['key'], unique=True)

    # Create analytics_summary table
    op.create_table('analytics_summary',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('date', sa.Date(), nullable=False),
        sa.Column('total_views', sa.Integer(), nullable=True),
        sa.Column('total_reads', sa.Integer(), nullable=True),
        sa.Column('total_claps', sa.Integer(), nullable=True),
        sa.Column('total_responses', sa.Integer(), nullable=True),
        sa.Column('articles_published', sa.Integer(), nullable=True),
        sa.Column('linkedin_posts', sa.Integer(), nullable=True),
        sa.Column('avg_read_ratio', sa.Numeric(precision=5, scale=4), nullable=True),
        sa.Column('avg_engagement_score', sa.Numeric(precision=8, scale=4), nullable=True),
        sa.Column('linkedin_impressions', sa.Integer(), nullable=True),
        sa.Column('linkedin_clicks', sa.Integer(), nullable=True),
        sa.Column('linkedin_likes', sa.Integer(), nullable=True),
        sa.Column('linkedin_comments', sa.Integer(), nullable=True),
        sa.Column('linkedin_shares', sa.Integer(), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('date', name='uq_analytics_summary_date')
    )
    op.create_index(op.f('ix_analytics_summary_date'), 'analytics_summary', ['date'], unique=False)
    op.create_index(op.f('ix_analytics_summary_id'), 'analytics_summary', ['id'], unique=False)


def downgrade() -> None:
    # Drop tables in reverse order
    op.drop_index(op.f('ix_analytics_summary_id'), table_name='analytics_summary')
    op.drop_index(op.f('ix_analytics_summary_date'), table_name='analytics_summary')
    op.drop_table('analytics_summary')
    
    op.drop_index(op.f('ix_user_settings_key'), table_name='user_settings')
    op.drop_index(op.f('ix_user_settings_id'), table_name='user_settings')
    op.drop_table('user_settings')
    
    op.drop_index(op.f('ix_linkedin_metrics_linkedin_post_id'), table_name='linkedin_metrics')
    op.drop_index(op.f('ix_linkedin_metrics_id'), table_name='linkedin_metrics')
    op.drop_index(op.f('ix_linkedin_metrics_collected_at'), table_name='linkedin_metrics')
    op.drop_table('linkedin_metrics')
    
    op.drop_index(op.f('ix_linkedin_posts_status'), table_name='linkedin_posts')
    op.drop_index(op.f('ix_linkedin_posts_scheduled_at'), table_name='linkedin_posts')
    op.drop_index(op.f('ix_linkedin_posts_published_at'), table_name='linkedin_posts')
    op.drop_index(op.f('ix_linkedin_posts_linkedin_post_id'), table_name='linkedin_posts')
    op.drop_index(op.f('ix_linkedin_posts_id'), table_name='linkedin_posts')
    op.drop_index(op.f('ix_linkedin_posts_article_id'), table_name='linkedin_posts')
    op.drop_table('linkedin_posts')
    
    op.drop_index(op.f('ix_article_metrics_id'), table_name='article_metrics')
    op.drop_index(op.f('ix_article_metrics_collected_at'), table_name='article_metrics')
    op.drop_index(op.f('ix_article_metrics_article_id'), table_name='article_metrics')
    op.drop_table('article_metrics')
    
    op.drop_index(op.f('ix_articles_published_at'), table_name='articles')
    op.drop_index(op.f('ix_articles_id'), table_name='articles')
    op.drop_index(op.f('ix_articles_guid'), table_name='articles')
    op.drop_table('articles')
