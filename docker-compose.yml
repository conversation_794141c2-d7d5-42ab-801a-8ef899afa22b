# =============================================================================
# MEDIUM ANALYTICS BOT - DOCKER COMPOSE PARA DESENVOLVIMENTO LOCAL
# =============================================================================

version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: medium_analytics_db
    environment:
      POSTGRES_DB: medium_analytics
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres123
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8"
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./backend/scripts/init_db.sql:/docker-entrypoint-initdb.d/init_db.sql
    networks:
      - medium_analytics_network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres -d medium_analytics"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: medium_analytics_redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - medium_analytics_network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
    command: redis-server --appendonly yes --maxmemory 256mb --maxmemory-policy allkeys-lru

  # FastAPI Backend
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: medium_analytics_backend
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=***********************************************/medium_analytics
      - REDIS_URL=redis://redis:6379
      - ENVIRONMENT=development
      - MEDIUM_RSS_URL=${MEDIUM_RSS_URL}
      - MEDIUM_USERNAME=${MEDIUM_USERNAME}
    volumes:
      - ./backend:/app
      - ./logs:/app/logs
    networks:
      - medium_analytics_network
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    restart: unless-stopped
    command: uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload

  # Streamlit Dashboard
  dashboard:
    build:
      context: ./dashboard
      dockerfile: Dockerfile
    container_name: medium_analytics_dashboard
    ports:
      - "8501:8501"
    environment:
      - DATABASE_URL=***********************************************/medium_analytics
      - REDIS_URL=redis://redis:6379
      - API_URL=http://backend:8000
    volumes:
      - ./dashboard:/app
    networks:
      - medium_analytics_network
    depends_on:
      - backend
    restart: unless-stopped
    command: streamlit run 📊_Overview.py --server.port=8501 --server.address=0.0.0.0

  # Celery Worker
  celery_worker:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: medium_analytics_worker
    environment:
      - DATABASE_URL=***********************************************/medium_analytics
      - REDIS_URL=redis://redis:6379
      - CELERY_BROKER_URL=redis://redis:6379/0
      - CELERY_RESULT_BACKEND=redis://redis:6379/0
    volumes:
      - ./backend:/app
      - ./logs:/app/logs
    networks:
      - medium_analytics_network
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    restart: unless-stopped
    command: celery -A app.tasks.celery worker --loglevel=info

  # Celery Beat (Scheduler)
  celery_beat:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: medium_analytics_scheduler
    environment:
      - DATABASE_URL=***********************************************/medium_analytics
      - REDIS_URL=redis://redis:6379
      - CELERY_BROKER_URL=redis://redis:6379/0
      - CELERY_RESULT_BACKEND=redis://redis:6379/0
    volumes:
      - ./backend:/app
      - ./logs:/app/logs
    networks:
      - medium_analytics_network
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    restart: unless-stopped
    command: celery -A app.tasks.celery beat --loglevel=info

  # Flower (Celery Monitoring)
  flower:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: medium_analytics_flower
    ports:
      - "5555:5555"
    environment:
      - CELERY_BROKER_URL=redis://redis:6379/0
      - CELERY_RESULT_BACKEND=redis://redis:6379/0
    networks:
      - medium_analytics_network
    depends_on:
      - redis
    restart: unless-stopped
    command: celery -A app.tasks.celery flower --port=5555

  # pgAdmin (Database Management)
  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: medium_analytics_pgadmin
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: admin123
      PGADMIN_CONFIG_SERVER_MODE: 'False'
    ports:
      - "5050:80"
    volumes:
      - pgadmin_data:/var/lib/pgadmin
    networks:
      - medium_analytics_network
    depends_on:
      - postgres
    restart: unless-stopped

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  pgadmin_data:
    driver: local

networks:
  medium_analytics_network:
    driver: bridge
    name: medium_analytics_network
