# =============================================================================
# MEDIUM ANALYTICS BOT - CONFIGURAÇÕES DE AMBIENTE
# =============================================================================

# AWS Configuration
AWS_REGION=us-east-1
AWS_ACCESS_KEY_ID=sua_access_key_aqui
AWS_SECRET_ACCESS_KEY=sua_secret_key_aqui

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================

# PostgreSQL (Local Development)
DB_HOST=localhost
DB_PORT=5432
DB_NAME=medium_analytics
DB_USER=postgres
DB_PASSWORD=postgres123
DATABASE_URL=postgresql://postgres:postgres123@localhost:5432/medium_analytics

# PostgreSQL (AWS RDS Production)
# DB_HOST=seu-rds-endpoint.amazonaws.com
# DB_PORT=5432
# DB_NAME=medium_analytics
# DB_USER=postgres
# DB_PASSWORD=sua_senha_segura
# DATABASE_URL=*************************************************/medium_analytics

# =============================================================================
# REDIS CONFIGURATION
# =============================================================================

# Redis (Local Development)
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_URL=redis://localhost:6379

# Redis (AWS ElastiCache Production)
# REDIS_HOST=seu-redis-endpoint.cache.amazonaws.com
# REDIS_PORT=6379
# REDIS_URL=redis://redis-endpoint:6379

# =============================================================================
# AWS SERVICES
# =============================================================================

# S3
S3_BUCKET_NAME=medium-analytics-bucket
S3_REGION=us-east-1

# SES (Email)
EMAIL_BACKEND=ses
AWS_SES_REGION=us-east-1
FROM_EMAIL=<EMAIL>
ADMIN_EMAIL=<EMAIL>

# CloudWatch
CLOUDWATCH_LOG_GROUP=/aws/medium-analytics
CLOUDWATCH_LOG_STREAM=application

# =============================================================================
# EXTERNAL APIS
# =============================================================================

# Medium RSS
MEDIUM_RSS_URL=https://medium.com/feed/@seu_usuario_medium
MEDIUM_USERNAME=seu_usuario_medium

# LinkedIn API
LINKEDIN_CLIENT_ID=seu_linkedin_client_id
LINKEDIN_CLIENT_SECRET=seu_linkedin_client_secret
LINKEDIN_ACCESS_TOKEN=seu_linkedin_access_token
LINKEDIN_REDIRECT_URI=http://localhost:8000/auth/linkedin/callback

# =============================================================================
# APPLICATION SETTINGS
# =============================================================================

# FastAPI
SECRET_KEY=sua_chave_secreta_super_segura_aqui
ENVIRONMENT=development
LOG_LEVEL=INFO
API_HOST=0.0.0.0
API_PORT=8000
DEBUG=True

# Streamlit
STREAMLIT_HOST=0.0.0.0
STREAMLIT_PORT=8501

# =============================================================================
# CELERY CONFIGURATION
# =============================================================================

# Celery Broker (Redis)
CELERY_BROKER_URL=redis://localhost:6379/0
CELERY_RESULT_BACKEND=redis://localhost:6379/0
CELERY_TASK_SERIALIZER=json
CELERY_RESULT_SERIALIZER=json
CELERY_ACCEPT_CONTENT=json
CELERY_TIMEZONE=UTC

# =============================================================================
# AUTOMATION SETTINGS
# =============================================================================

# RSS Check Frequency (minutes)
RSS_CHECK_INTERVAL=60

# LinkedIn Post Delay (hours after Medium publication)
LINKEDIN_POST_DELAY=2

# Analytics Collection Frequency (hours)
ANALYTICS_COLLECTION_INTERVAL=6

# Backup Frequency (hours)
BACKUP_INTERVAL=24

# =============================================================================
# MONITORING & ALERTS
# =============================================================================

# Email Notifications
ENABLE_EMAIL_ALERTS=true
WEEKLY_REPORT_DAY=sunday
WEEKLY_REPORT_TIME=09:00

# Performance Thresholds
HIGH_PERFORMANCE_VIEWS_THRESHOLD=1000
HIGH_PERFORMANCE_READ_RATIO_THRESHOLD=0.5

# =============================================================================
# DEVELOPMENT SETTINGS
# =============================================================================

# Database
SQLALCHEMY_ECHO=false
ALEMBIC_CONFIG=alembic.ini

# Testing
TESTING=false
TEST_DATABASE_URL=postgresql://postgres:postgres123@localhost:5432/medium_analytics_test

# =============================================================================
# SECURITY
# =============================================================================

# CORS
CORS_ORIGINS=["http://localhost:3000", "http://localhost:8501", "http://localhost:8000"]
CORS_ALLOW_CREDENTIALS=true
CORS_ALLOW_METHODS=["*"]
CORS_ALLOW_HEADERS=["*"]

# Rate Limiting
RATE_LIMIT_PER_MINUTE=60
RATE_LIMIT_BURST=10
