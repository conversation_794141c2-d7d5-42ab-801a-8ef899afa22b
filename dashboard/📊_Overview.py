# =============================================================================
# MEDIUM ANALYTICS BOT - DASHBOARD PRINCIPAL
# =============================================================================

import streamlit as st
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import pandas as pd
import requests
from datetime import datetime, timedelta
import os
from typing import Optional, Dict, Any

# Configuração da página
st.set_page_config(
    page_title="Medium Analytics - Overview",
    page_icon="📊",
    layout="wide",
    initial_sidebar_state="expanded"
)

# =============================================================================
# CONFIGURAÇÕES E CONEXÕES
# =============================================================================

# URLs da API
API_BASE_URL = os.getenv("API_URL", "http://localhost:8000")
API_PREFIX = "/api/v1"

def get_api_url(endpoint: str) -> str:
    """Construir URL da API"""
    return f"{API_BASE_URL}{API_PREFIX}{endpoint}"

# =============================================================================
# FUNÇÕES DE DADOS
# =============================================================================

@st.cache_data(ttl=300)  # Cache por 5 minutos
def fetch_articles_summary(days: int = 30) -> Optional[Dict[str, Any]]:
    """Buscar resumo dos artigos"""
    try:
        response = requests.get(
            get_api_url(f"/articles/stats/summary?days={days}"),
            timeout=10
        )
        response.raise_for_status()
        return response.json()
    except Exception as e:
        st.error(f"Erro ao buscar resumo dos artigos: {e}")
        return None

@st.cache_data(ttl=300)
def fetch_analytics_summary(days: int = 30) -> Optional[Dict[str, Any]]:
    """Buscar resumo analítico"""
    try:
        response = requests.get(
            get_api_url(f"/analytics/summary?days={days}"),
            timeout=10
        )
        response.raise_for_status()
        return response.json()
    except Exception as e:
        st.error(f"Erro ao buscar analytics: {e}")
        return None

@st.cache_data(ttl=300)
def fetch_daily_analytics(days: int = 30) -> Optional[Dict[str, Any]]:
    """Buscar analytics diários"""
    try:
        response = requests.get(
            get_api_url(f"/analytics/daily?days={days}"),
            timeout=10
        )
        response.raise_for_status()
        return response.json()
    except Exception as e:
        st.error(f"Erro ao buscar analytics diários: {e}")
        return None

@st.cache_data(ttl=300)
def fetch_articles(limit: int = 20) -> Optional[list]:
    """Buscar lista de artigos"""
    try:
        response = requests.get(
            get_api_url(f"/articles/?limit={limit}"),
            timeout=10
        )
        response.raise_for_status()
        return response.json()
    except Exception as e:
        st.error(f"Erro ao buscar artigos: {e}")
        return None

@st.cache_data(ttl=300)
def fetch_popular_tags(days: int = 30, limit: int = 10) -> Optional[list]:
    """Buscar tags populares"""
    try:
        response = requests.get(
            get_api_url(f"/articles/tags/popular?days={days}&limit={limit}"),
            timeout=10
        )
        response.raise_for_status()
        return response.json()
    except Exception as e:
        st.error(f"Erro ao buscar tags: {e}")
        return None

def check_api_health() -> bool:
    """Verificar se a API está funcionando"""
    try:
        response = requests.get(f"{API_BASE_URL}/health", timeout=5)
        return response.status_code == 200
    except:
        return False

# =============================================================================
# INTERFACE PRINCIPAL
# =============================================================================

def main():
    """Interface principal do dashboard"""
    
    # Header
    st.title("📊 Medium Analytics Dashboard")
    st.markdown("---")
    
    # Verificar conexão com API
    if not check_api_health():
        st.error("🔴 **API não está disponível**")
        st.info("Certifique-se de que o backend está rodando em `http://localhost:8000`")
        st.code("make start  # ou docker-compose up -d")
        return
    
    # Sidebar para configurações
    with st.sidebar:
        st.header("⚙️ Configurações")
        
        # Período de análise
        period_days = st.selectbox(
            "📅 Período de análise",
            options=[7, 15, 30, 60, 90],
            index=2,  # 30 dias por padrão
            help="Selecione o período para análise dos dados"
        )
        
        # Botão de refresh
        if st.button("🔄 Atualizar Dados"):
            st.cache_data.clear()
            st.rerun()
        
        # Informações da API
        st.markdown("---")
        st.markdown("**🔗 API Status**")
        st.success("✅ Conectado")
        st.caption(f"Base URL: {API_BASE_URL}")
    
    # Buscar dados
    with st.spinner("📊 Carregando dados..."):
        articles_summary = fetch_articles_summary(period_days)
        analytics_summary = fetch_analytics_summary(period_days)
        daily_analytics = fetch_daily_analytics(period_days)
        articles = fetch_articles(20)
        popular_tags = fetch_popular_tags(period_days, 10)
    
    # Verificar se temos dados
    if not articles_summary or not analytics_summary:
        st.warning("📭 **Nenhum dado encontrado**")
        st.info("Isso pode acontecer se:")
        st.markdown("""
        - O RSS do Medium não está configurado
        - Ainda não há artigos sincronizados
        - O banco de dados está vazio
        """)
        
        # Botão para sincronizar
        if st.button("🔄 Sincronizar Artigos do Medium"):
            try:
                response = requests.post(get_api_url("/articles/sync"))
                if response.status_code == 200:
                    result = response.json()
                    st.success(f"✅ {result['new_articles']} novos artigos sincronizados!")
                    st.rerun()
                else:
                    st.error("❌ Erro na sincronização. Verifique as configurações do RSS.")
            except Exception as e:
                st.error(f"❌ Erro: {e}")
        
        return
    
    # =============================================================================
    # MÉTRICAS PRINCIPAIS
    # =============================================================================
    
    st.subheader("📈 Métricas Principais")
    
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.metric(
            label="👀 Views Totais",
            value=f"{articles_summary.get('total_views', 0):,}",
            delta=None
        )
    
    with col2:
        st.metric(
            label="📖 Reads Totais", 
            value=f"{articles_summary.get('total_reads', 0):,}",
            delta=None
        )
    
    with col3:
        read_ratio = articles_summary.get('avg_read_ratio', 0)
        st.metric(
            label="📊 Taxa de Leitura",
            value=f"{read_ratio:.1%}",
            delta=None
        )
    
    with col4:
        st.metric(
            label="📝 Artigos",
            value=articles_summary.get('total_articles', 0),
            delta=None
        )
    
    # =============================================================================
    # GRÁFICOS PRINCIPAIS
    # =============================================================================
    
    st.markdown("---")
    
    # Row 1: Gráficos de tendência
    col1, col2 = st.columns(2)
    
    with col1:
        st.subheader("📈 Tendência de Views")
        
        if daily_analytics and daily_analytics.get('daily_data'):
            df_daily = pd.DataFrame(daily_analytics['daily_data'])
            df_daily['date'] = pd.to_datetime(df_daily['date'])
            
            fig = px.line(
                df_daily,
                x='date',
                y='total_views',
                title="Views por Dia",
                labels={'total_views': 'Views', 'date': 'Data'}
            )
            fig.update_layout(height=400)
            st.plotly_chart(fig, use_container_width=True)
        else:
            st.info("📊 Dados insuficientes para gráfico de tendência")
    
    with col2:
        st.subheader("📊 Performance dos Artigos")
        
        if articles:
            # Top 10 artigos por views
            df_articles = pd.DataFrame(articles)
            df_top = df_articles.nlargest(10, 'total_views')
            
            fig = px.bar(
                df_top,
                x='total_views',
                y='title',
                orientation='h',
                title="Top 10 Artigos por Views",
                labels={'total_views': 'Views', 'title': 'Artigo'}
            )
            fig.update_layout(
                height=400,
                yaxis_title=None
            )
            st.plotly_chart(fig, use_container_width=True)
        else:
            st.info("📊 Nenhum artigo encontrado")
    
    # Row 2: Métricas detalhadas
    st.markdown("---")
    col1, col2 = st.columns(2)
    
    with col1:
        st.subheader("🏷️ Tags Populares")
        
        if popular_tags:
            df_tags = pd.DataFrame(popular_tags)
            
            fig = px.pie(
                df_tags,
                values='count',
                names='tag',
                title="Distribuição de Tags"
            )
            fig.update_layout(height=400)
            st.plotly_chart(fig, use_container_width=True)
        else:
            st.info("🏷️ Nenhuma tag encontrada")
    
    with col2:
        st.subheader("📊 Distribuição de Métricas")
        
        if articles:
            df_articles = pd.DataFrame(articles)
            
            # Gráfico de dispersão: Views vs Read Ratio
            fig = px.scatter(
                df_articles,
                x='total_views',
                y='read_ratio',
                size='total_claps',
                hover_data=['title'],
                title="Views vs Taxa de Leitura",
                labels={
                    'total_views': 'Views',
                    'read_ratio': 'Taxa de Leitura',
                    'total_claps': 'Claps'
                }
            )
            fig.update_layout(height=400)
            st.plotly_chart(fig, use_container_width=True)
        else:
            st.info("📊 Dados insuficientes para análise")
    
    # =============================================================================
    # TABELA DE ARTIGOS
    # =============================================================================
    
    st.markdown("---")
    st.subheader("📝 Artigos Recentes")
    
    if articles:
        # Preparar dados para exibição
        df_display = pd.DataFrame(articles)
        
        # Selecionar e renomear colunas
        columns_to_show = {
            'title': 'Título',
            'published_at': 'Publicado em',
            'total_views': 'Views',
            'total_reads': 'Reads',
            'total_claps': 'Claps',
            'read_ratio': 'Taxa de Leitura'
        }
        
        df_display = df_display[list(columns_to_show.keys())].rename(columns=columns_to_show)
        
        # Formatar dados
        df_display['Publicado em'] = pd.to_datetime(df_display['Publicado em']).dt.strftime('%d/%m/%Y %H:%M')
        df_display['Taxa de Leitura'] = df_display['Taxa de Leitura'].apply(lambda x: f"{x:.1%}")
        
        # Truncar títulos longos
        df_display['Título'] = df_display['Título'].apply(
            lambda x: x[:80] + "..." if len(x) > 80 else x
        )
        
        st.dataframe(
            df_display,
            use_container_width=True,
            hide_index=True
        )
    else:
        st.info("📝 Nenhum artigo encontrado")
    
    # =============================================================================
    # FOOTER
    # =============================================================================
    
    st.markdown("---")
    col1, col2, col3 = st.columns(3)
    
    with col1:
        st.caption(f"🔄 Última atualização: {datetime.now().strftime('%H:%M:%S')}")
    
    with col2:
        st.caption(f"📅 Período: {period_days} dias")
    
    with col3:
        st.caption("🚀 Medium Analytics Bot v1.0")


# =============================================================================
# EXECUTAR APLICAÇÃO
# =============================================================================

if __name__ == "__main__":
    main()
