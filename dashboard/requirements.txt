# =============================================================================
# MEDIUM ANALYTICS BOT - DASHBOARD DEPENDENCIES
# =============================================================================

# Streamlit Framework
streamlit==1.28.2
streamlit-option-menu==0.3.6
streamlit-aggrid==0.3.4
streamlit-authenticator==0.2.3

# Data Visualization
plotly==5.17.0
matplotlib==3.8.2
seaborn==0.13.0
altair==5.2.0

# Data Processing
pandas==2.1.4
numpy==1.25.2

# Database
psycopg2-binary==2.9.9
sqlalchemy==2.0.23

# Redis
redis==5.0.1

# AWS SDK
boto3==1.34.0

# HTTP Requests
requests==2.31.0
httpx==0.25.2

# Configuration
python-dotenv==1.0.0

# Date & Time
python-dateutil==2.8.2
pytz==2023.3

# Utilities
click==8.1.7
rich==13.7.0

# Image Processing
pillow==10.1.0

# File Processing
openpyxl==3.1.2

# Caching
streamlit-cache==0.1.3

# Authentication
streamlit-oauth==0.1.0

# Components
streamlit-elements==0.1.0
streamlit-card==0.0.61
streamlit-metrics==0.1.0

# Charts & Graphs
plotly-express==0.4.1
pygments==2.17.2

# Data Export
xlsxwriter==3.1.9

# Logging
loguru==0.7.2

# JSON Processing
orjson==3.9.10

# Validation
validators==0.22.0

# Async Support
asyncio==3.4.3

# Development
watchdog==3.0.0
