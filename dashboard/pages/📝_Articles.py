# =============================================================================
# MEDIUM ANALYTICS BOT - PÁGINA DE ARTIGOS
# =============================================================================

import streamlit as st
import plotly.express as px
import plotly.graph_objects as go
import pandas as pd
import requests
from datetime import datetime, timedelta
import os

# Configuração da página
st.set_page_config(
    page_title="Medium Analytics - Artigos",
    page_icon="📝",
    layout="wide"
)

# URLs da API
API_BASE_URL = os.getenv("API_URL", "http://localhost:8000")
API_PREFIX = "/api/v1"

def get_api_url(endpoint: str) -> str:
    """Construir URL da API"""
    return f"{API_BASE_URL}{API_PREFIX}{endpoint}"

# =============================================================================
# FUNÇÕES DE DADOS
# =============================================================================

@st.cache_data(ttl=300)
def fetch_articles(skip: int = 0, limit: int = 50, search: str = "", tags: str = "", days: int = None):
    """Buscar artigos com filtros"""
    try:
        params = {"skip": skip, "limit": limit}
        if search:
            params["search"] = search
        if tags:
            params["tags"] = tags
        if days:
            params["days"] = days
            
        response = requests.get(get_api_url("/articles/"), params=params, timeout=10)
        response.raise_for_status()
        return response.json()
    except Exception as e:
        st.error(f"Erro ao buscar artigos: {e}")
        return []

@st.cache_data(ttl=300)
def fetch_article_metrics(article_id: int, days: int = 30):
    """Buscar métricas de um artigo"""
    try:
        response = requests.get(
            get_api_url(f"/articles/{article_id}/metrics?days={days}"),
            timeout=10
        )
        response.raise_for_status()
        return response.json()
    except Exception as e:
        st.error(f"Erro ao buscar métricas: {e}")
        return []

@st.cache_data(ttl=300)
def fetch_popular_tags(days: int = 30, limit: int = 20):
    """Buscar tags populares"""
    try:
        response = requests.get(
            get_api_url(f"/articles/tags/popular?days={days}&limit={limit}"),
            timeout=10
        )
        response.raise_for_status()
        return response.json()
    except Exception as e:
        st.error(f"Erro ao buscar tags: {e}")
        return []

def sync_articles():
    """Sincronizar artigos do Medium"""
    try:
        response = requests.post(get_api_url("/articles/sync"), timeout=30)
        response.raise_for_status()
        return response.json()
    except Exception as e:
        st.error(f"Erro na sincronização: {e}")
        return None

# =============================================================================
# INTERFACE PRINCIPAL
# =============================================================================

def main():
    st.title("📝 Gestão de Artigos")
    st.markdown("---")
    
    # Sidebar com filtros
    with st.sidebar:
        st.header("🔍 Filtros")
        
        # Busca por texto
        search_term = st.text_input(
            "🔎 Buscar por título",
            placeholder="Digite palavras-chave..."
        )
        
        # Filtro por período
        period_filter = st.selectbox(
            "📅 Período",
            options=[None, 7, 15, 30, 60, 90],
            format_func=lambda x: "Todos" if x is None else f"Últimos {x} dias",
            index=0
        )
        
        # Filtro por tags
        popular_tags = fetch_popular_tags(30, 20)
        if popular_tags:
            tag_options = [""] + [tag["tag"] for tag in popular_tags]
            selected_tags = st.multiselect(
                "🏷️ Tags",
                options=tag_options[1:],  # Excluir opção vazia
                help="Selecione uma ou mais tags"
            )
            tags_filter = ",".join(selected_tags) if selected_tags else ""
        else:
            tags_filter = ""
        
        st.markdown("---")
        
        # Ações
        st.header("⚡ Ações")
        
        if st.button("🔄 Sincronizar Medium", help="Buscar novos artigos do RSS"):
            with st.spinner("Sincronizando..."):
                result = sync_articles()
                if result:
                    st.success(f"✅ {result.get('new_articles', 0)} novos artigos!")
                    st.cache_data.clear()
                    st.rerun()
        
        if st.button("🔄 Atualizar Dados"):
            st.cache_data.clear()
            st.rerun()
    
    # Buscar artigos com filtros
    articles = fetch_articles(
        skip=0,
        limit=100,
        search=search_term,
        tags=tags_filter,
        days=period_filter
    )
    
    if not articles:
        st.warning("📭 Nenhum artigo encontrado com os filtros aplicados")
        return
    
    # =============================================================================
    # ESTATÍSTICAS GERAIS
    # =============================================================================
    
    st.subheader("📊 Estatísticas")
    
    col1, col2, col3, col4 = st.columns(4)
    
    total_views = sum(article.get('total_views', 0) for article in articles)
    total_reads = sum(article.get('total_reads', 0) for article in articles)
    total_claps = sum(article.get('total_claps', 0) for article in articles)
    avg_read_ratio = (total_reads / total_views) if total_views > 0 else 0
    
    with col1:
        st.metric("📝 Artigos", len(articles))
    
    with col2:
        st.metric("👀 Views Totais", f"{total_views:,}")
    
    with col3:
        st.metric("📖 Reads Totais", f"{total_reads:,}")
    
    with col4:
        st.metric("📊 Taxa de Leitura", f"{avg_read_ratio:.1%}")
    
    # =============================================================================
    # GRÁFICOS DE ANÁLISE
    # =============================================================================
    
    st.markdown("---")
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.subheader("📈 Top Performers")
        
        # Top 10 por views
        df_articles = pd.DataFrame(articles)
        df_top = df_articles.nlargest(10, 'total_views')
        
        fig = px.bar(
            df_top,
            x='total_views',
            y='title',
            orientation='h',
            title="Top 10 por Views",
            labels={'total_views': 'Views', 'title': 'Artigo'}
        )
        fig.update_layout(
            height=500,
            yaxis_title=None
        )
        # Truncar títulos para melhor visualização
        fig.update_traces(
            text=[title[:40] + "..." if len(title) > 40 else title for title in df_top['title']],
            textposition='inside'
        )
        st.plotly_chart(fig, use_container_width=True)
    
    with col2:
        st.subheader("🎯 Análise de Engajamento")
        
        # Scatter plot: Views vs Read Ratio
        fig = px.scatter(
            df_articles,
            x='total_views',
            y='read_ratio',
            size='total_claps',
            color='total_claps',
            hover_data=['title'],
            title="Views vs Taxa de Leitura",
            labels={
                'total_views': 'Views',
                'read_ratio': 'Taxa de Leitura',
                'total_claps': 'Claps'
            }
        )
        fig.update_layout(height=500)
        st.plotly_chart(fig, use_container_width=True)
    
    # =============================================================================
    # LISTA DETALHADA DE ARTIGOS
    # =============================================================================
    
    st.markdown("---")
    st.subheader("📋 Lista de Artigos")
    
    # Opções de ordenação
    col1, col2 = st.columns([3, 1])
    
    with col2:
        sort_by = st.selectbox(
            "Ordenar por:",
            options=['published_at', 'total_views', 'total_reads', 'total_claps', 'read_ratio'],
            format_func=lambda x: {
                'published_at': 'Data de Publicação',
                'total_views': 'Views',
                'total_reads': 'Reads',
                'total_claps': 'Claps',
                'read_ratio': 'Taxa de Leitura'
            }[x],
            index=0
        )
    
    # Ordenar artigos
    df_articles = pd.DataFrame(articles)
    if sort_by == 'published_at':
        df_articles = df_articles.sort_values(sort_by, ascending=False)
    else:
        df_articles = df_articles.sort_values(sort_by, ascending=False)
    
    # Exibir artigos em cards
    for idx, article in df_articles.iterrows():
        with st.expander(
            f"📝 {article['title'][:80]}{'...' if len(article['title']) > 80 else ''}",
            expanded=False
        ):
            col1, col2 = st.columns([2, 1])
            
            with col1:
                st.markdown(f"**📅 Publicado:** {pd.to_datetime(article['published_at']).strftime('%d/%m/%Y %H:%M')}")
                st.markdown(f"**🔗 URL:** [Abrir artigo]({article['url']})")
                
                if article.get('tags'):
                    tags_str = " ".join([f"`{tag}`" for tag in article['tags']])
                    st.markdown(f"**🏷️ Tags:** {tags_str}")
                
                if article.get('summary'):
                    st.markdown(f"**📄 Resumo:** {article['summary'][:200]}...")
            
            with col2:
                st.metric("👀 Views", f"{article['total_views']:,}")
                st.metric("📖 Reads", f"{article['total_reads']:,}")
                st.metric("👏 Claps", f"{article['total_claps']:,}")
                st.metric("📊 Taxa de Leitura", f"{article['read_ratio']:.1%}")
                
                # Botão para ver métricas detalhadas
                if st.button(f"📈 Ver Métricas", key=f"metrics_{article['id']}"):
                    st.session_state[f"show_metrics_{article['id']}"] = True
            
            # Mostrar gráfico de métricas se solicitado
            if st.session_state.get(f"show_metrics_{article['id']}", False):
                metrics = fetch_article_metrics(article['id'], 30)
                
                if metrics:
                    df_metrics = pd.DataFrame(metrics)
                    df_metrics['collected_at'] = pd.to_datetime(df_metrics['collected_at'])
                    
                    fig = px.line(
                        df_metrics,
                        x='collected_at',
                        y=['views', 'reads', 'claps'],
                        title=f"Evolução das Métricas - {article['title'][:50]}...",
                        labels={'collected_at': 'Data', 'value': 'Quantidade'}
                    )
                    fig.update_layout(height=300)
                    st.plotly_chart(fig, use_container_width=True)
                    
                    if st.button(f"❌ Fechar", key=f"close_metrics_{article['id']}"):
                        st.session_state[f"show_metrics_{article['id']}"] = False
                        st.rerun()
                else:
                    st.info("📊 Nenhuma métrica histórica disponível")
    
    # =============================================================================
    # FOOTER
    # =============================================================================
    
    st.markdown("---")
    st.caption(f"📊 Mostrando {len(articles)} artigos | Última atualização: {datetime.now().strftime('%H:%M:%S')}")


if __name__ == "__main__":
    main()
