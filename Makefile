# =============================================================================
# MEDIUM ANALYTICS BOT - MAKEFILE
# =============================================================================

.PHONY: help setup start stop restart logs clean build test lint format

# Variáveis
DOCKER_COMPOSE = docker-compose
PYTHON = python3
PIP = pip3

# Cores para output
GREEN = \033[0;32m
YELLOW = \033[1;33m
RED = \033[0;31m
NC = \033[0m # No Color

# Help
help: ## Mostrar esta ajuda
	@echo "$(GREEN)Medium Analytics Bot - Comandos Disponíveis$(NC)"
	@echo "=============================================="
	@awk 'BEGIN {FS = ":.*?## "} /^[a-zA-Z_-]+:.*?## / {printf "$(YELLOW)%-20s$(NC) %s\n", $$1, $$2}' $(MAKEFILE_LIST)

# Setup inicial
setup: ## Executar setup inicial completo
	@echo "$(GREEN)Executando setup inicial...$(NC)"
	./setup.sh

# Desenvolvimento
start: ## Iniciar todos os serviços
	@echo "$(GREEN)Iniciando serviços...$(NC)"
	$(DOCKER_COMPOSE) up -d

start-dev: ## Iniciar apenas serviços básicos (postgres, redis)
	@echo "$(GREEN)Iniciando serviços básicos...$(NC)"
	$(DOCKER_COMPOSE) up -d postgres redis

stop: ## Parar todos os serviços
	@echo "$(YELLOW)Parando serviços...$(NC)"
	$(DOCKER_COMPOSE) down

restart: ## Reiniciar todos os serviços
	@echo "$(YELLOW)Reiniciando serviços...$(NC)"
	$(DOCKER_COMPOSE) down
	$(DOCKER_COMPOSE) up -d

# Logs
logs: ## Mostrar logs de todos os serviços
	$(DOCKER_COMPOSE) logs -f

logs-backend: ## Mostrar logs do backend
	$(DOCKER_COMPOSE) logs -f backend

logs-dashboard: ## Mostrar logs do dashboard
	$(DOCKER_COMPOSE) logs -f dashboard

logs-worker: ## Mostrar logs do Celery worker
	$(DOCKER_COMPOSE) logs -f celery_worker

# Build
build: ## Construir todas as imagens
	@echo "$(GREEN)Construindo imagens...$(NC)"
	$(DOCKER_COMPOSE) build

build-backend: ## Construir apenas imagem do backend
	$(DOCKER_COMPOSE) build backend

build-dashboard: ## Construir apenas imagem do dashboard
	$(DOCKER_COMPOSE) build dashboard

# Database
db-migrate: ## Executar migrações do banco
	@echo "$(GREEN)Executando migrações...$(NC)"
	$(DOCKER_COMPOSE) exec backend alembic upgrade head

db-migration: ## Criar nova migração
	@read -p "Nome da migração: " name; \
	$(DOCKER_COMPOSE) exec backend alembic revision --autogenerate -m "$$name"

db-reset: ## Resetar banco de dados (CUIDADO!)
	@echo "$(RED)ATENÇÃO: Isso vai apagar todos os dados!$(NC)"
	@read -p "Tem certeza? (y/N): " confirm; \
	if [ "$$confirm" = "y" ] || [ "$$confirm" = "Y" ]; then \
		$(DOCKER_COMPOSE) down -v; \
		$(DOCKER_COMPOSE) up -d postgres redis; \
		sleep 5; \
		$(DOCKER_COMPOSE) exec backend alembic upgrade head; \
	fi

db-shell: ## Conectar ao PostgreSQL
	$(DOCKER_COMPOSE) exec postgres psql -U postgres -d medium_analytics

# Desenvolvimento local
dev-install: ## Instalar dependências localmente
	@echo "$(GREEN)Instalando dependências...$(NC)"
	$(PIP) install -r backend/requirements.txt
	$(PIP) install -r dashboard/requirements.txt

dev-backend: ## Executar backend localmente
	cd backend && uvicorn app.main:app --reload --host 0.0.0.0 --port 8000

dev-dashboard: ## Executar dashboard localmente
	cd dashboard && streamlit run 📊_Overview.py --server.port 8501

dev-worker: ## Executar Celery worker localmente
	cd backend && celery -A app.tasks.celery worker --loglevel=info

dev-beat: ## Executar Celery beat localmente
	cd backend && celery -A app.tasks.celery beat --loglevel=info

# Testes
test: ## Executar todos os testes
	@echo "$(GREEN)Executando testes...$(NC)"
	$(DOCKER_COMPOSE) exec backend pytest

test-coverage: ## Executar testes com coverage
	$(DOCKER_COMPOSE) exec backend pytest --cov=app --cov-report=html

# Qualidade de código
lint: ## Executar linting
	@echo "$(GREEN)Executando linting...$(NC)"
	$(DOCKER_COMPOSE) exec backend flake8 app/
	$(DOCKER_COMPOSE) exec backend mypy app/

format: ## Formatar código
	@echo "$(GREEN)Formatando código...$(NC)"
	$(DOCKER_COMPOSE) exec backend black app/
	$(DOCKER_COMPOSE) exec backend isort app/

# Monitoramento
status: ## Mostrar status dos serviços
	@echo "$(GREEN)Status dos serviços:$(NC)"
	$(DOCKER_COMPOSE) ps

health: ## Verificar saúde dos serviços
	@echo "$(GREEN)Verificando saúde dos serviços...$(NC)"
	@echo "PostgreSQL:"
	@$(DOCKER_COMPOSE) exec postgres pg_isready -U postgres || echo "$(RED)PostgreSQL não está pronto$(NC)"
	@echo "Redis:"
	@$(DOCKER_COMPOSE) exec redis redis-cli ping || echo "$(RED)Redis não está pronto$(NC)"

# Backup
backup: ## Fazer backup do banco
	@echo "$(GREEN)Fazendo backup...$(NC)"
	mkdir -p backups
	$(DOCKER_COMPOSE) exec postgres pg_dump -U postgres medium_analytics > backups/backup_$(shell date +%Y%m%d_%H%M%S).sql

# Limpeza
clean: ## Limpar containers e volumes
	@echo "$(YELLOW)Limpando containers e volumes...$(NC)"
	$(DOCKER_COMPOSE) down -v --remove-orphans
	docker system prune -f

clean-all: ## Limpeza completa (incluindo imagens)
	@echo "$(RED)Limpeza completa...$(NC)"
	$(DOCKER_COMPOSE) down -v --remove-orphans
	docker system prune -af

# Produção
deploy-aws: ## Deploy na AWS (requer configuração)
	@echo "$(GREEN)Fazendo deploy na AWS...$(NC)"
	cd terraform && terraform apply

# Utilitários
shell-backend: ## Shell no container do backend
	$(DOCKER_COMPOSE) exec backend bash

shell-dashboard: ## Shell no container do dashboard
	$(DOCKER_COMPOSE) exec dashboard bash

shell-postgres: ## Shell no container do PostgreSQL
	$(DOCKER_COMPOSE) exec postgres bash

shell-redis: ## Shell no container do Redis
	$(DOCKER_COMPOSE) exec redis sh

# URLs úteis
urls: ## Mostrar URLs importantes
	@echo "$(GREEN)URLs importantes:$(NC)"
	@echo "Dashboard:     http://localhost:8501"
	@echo "API:           http://localhost:8000"
	@echo "API Docs:      http://localhost:8000/docs"
	@echo "pgAdmin:       http://localhost:5050"
	@echo "Flower:        http://localhost:5555"

# Configuração
env: ## Criar arquivo .env a partir do exemplo
	@if [ ! -f .env ]; then \
		cp .env.example .env; \
		echo "$(GREEN)Arquivo .env criado! Edite com suas configurações.$(NC)"; \
	else \
		echo "$(YELLOW)Arquivo .env já existe.$(NC)"; \
	fi
