#!/bin/bash

# =============================================================================
# MEDIUM ANALYTICS BOT - SCRIPT DE SETUP INICIAL
# =============================================================================

set -e

echo "🚀 Configurando Medium Analytics Bot..."
echo "======================================"

# Cores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Função para imprimir mensagens coloridas
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Verificar dependências
check_dependencies() {
    print_status "Verificando dependências..."
    
    # Docker
    if ! command -v docker &> /dev/null; then
        print_error "Docker não encontrado. Instale o Docker primeiro."
        exit 1
    fi
    
    # Docker Compose
    if ! command -v docker-compose &> /dev/null; then
        print_error "Docker Compose não encontrado. Instale o Docker Compose primeiro."
        exit 1
    fi
    
    # Python (opcional, para desenvolvimento local)
    if command -v python3 &> /dev/null; then
        PYTHON_VERSION=$(python3 --version | cut -d' ' -f2 | cut -d'.' -f1,2)
        print_success "Python $PYTHON_VERSION encontrado"
    else
        print_warning "Python3 não encontrado. Recomendado para desenvolvimento local."
    fi
    
    print_success "Dependências verificadas!"
}

# Configurar arquivo .env
setup_env() {
    print_status "Configurando arquivo .env..."
    
    if [ ! -f .env ]; then
        cp .env.example .env
        print_success "Arquivo .env criado a partir do .env.example"
        print_warning "IMPORTANTE: Edite o arquivo .env com suas configurações!"
        print_warning "Especialmente: MEDIUM_RSS_URL, LinkedIn API credentials"
    else
        print_warning "Arquivo .env já existe. Não foi sobrescrito."
    fi
}

# Criar diretórios necessários
create_directories() {
    print_status "Criando diretórios necessários..."
    
    mkdir -p logs
    mkdir -p backups
    mkdir -p data
    
    print_success "Diretórios criados!"
}

# Configurar ambiente de desenvolvimento local
setup_local_dev() {
    print_status "Configurando ambiente de desenvolvimento local..."
    
    # Criar ambiente virtual Python (opcional)
    if command -v python3 &> /dev/null; then
        if [ ! -d "venv" ]; then
            python3 -m venv venv
            print_success "Ambiente virtual Python criado"
            print_status "Para ativar: source venv/bin/activate"
        fi
    fi
    
    # Verificar se Docker está rodando
    if ! docker info &> /dev/null; then
        print_error "Docker não está rodando. Inicie o Docker primeiro."
        exit 1
    fi
    
    print_success "Ambiente de desenvolvimento configurado!"
}

# Inicializar serviços Docker
start_services() {
    print_status "Iniciando serviços Docker..."
    
    # Parar serviços existentes
    docker-compose down 2>/dev/null || true
    
    # Construir e iniciar serviços
    docker-compose up -d postgres redis
    
    print_status "Aguardando serviços ficarem prontos..."
    sleep 10
    
    # Verificar se PostgreSQL está pronto
    if docker-compose exec postgres pg_isready -U postgres &> /dev/null; then
        print_success "PostgreSQL está pronto!"
    else
        print_error "PostgreSQL não está respondendo"
        exit 1
    fi
    
    # Verificar se Redis está pronto
    if docker-compose exec redis redis-cli ping &> /dev/null; then
        print_success "Redis está pronto!"
    else
        print_error "Redis não está respondendo"
        exit 1
    fi
    
    print_success "Serviços básicos iniciados!"
}

# Mostrar informações finais
show_info() {
    echo ""
    echo "🎉 Setup concluído com sucesso!"
    echo "=============================="
    echo ""
    echo "📋 Próximos passos:"
    echo "1. Edite o arquivo .env com suas configurações"
    echo "2. Configure sua URL do Medium RSS"
    echo "3. Configure as credenciais da API do LinkedIn"
    echo ""
    echo "🚀 Para iniciar o desenvolvimento:"
    echo "   docker-compose up -d"
    echo ""
    echo "🌐 URLs importantes:"
    echo "   • Dashboard: http://localhost:8501"
    echo "   • API: http://localhost:8000"
    echo "   • API Docs: http://localhost:8000/docs"
    echo "   • pgAdmin: http://localhost:5050"
    echo "   • Flower (Celery): http://localhost:5555"
    echo ""
    echo "📊 Credenciais pgAdmin:"
    echo "   • Email: <EMAIL>"
    echo "   • Senha: admin123"
    echo ""
    echo "🔧 Comandos úteis:"
    echo "   • Ver logs: docker-compose logs -f"
    echo "   • Parar tudo: docker-compose down"
    echo "   • Rebuild: docker-compose up --build"
    echo ""
    echo "📚 Consulte o README.md para mais informações!"
}

# Menu principal
main() {
    echo "Escolha uma opção:"
    echo "1) Setup completo (recomendado para primeira vez)"
    echo "2) Apenas verificar dependências"
    echo "3) Apenas configurar .env"
    echo "4) Apenas iniciar serviços Docker"
    echo "5) Sair"
    
    read -p "Digite sua escolha (1-5): " choice
    
    case $choice in
        1)
            check_dependencies
            setup_env
            create_directories
            setup_local_dev
            start_services
            show_info
            ;;
        2)
            check_dependencies
            ;;
        3)
            setup_env
            ;;
        4)
            start_services
            ;;
        5)
            echo "Saindo..."
            exit 0
            ;;
        *)
            print_error "Opção inválida!"
            exit 1
            ;;
    esac
}

# Verificar se está sendo executado como script
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
